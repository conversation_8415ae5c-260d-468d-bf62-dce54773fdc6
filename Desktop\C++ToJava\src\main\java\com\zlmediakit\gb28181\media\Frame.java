package com.zlmediakit.gb28181.media;

/**
 * 媒体帧接口
 * 基于ZLMediaKit的Frame实现
 * 
 * <AUTHOR> Java Port
 */
public interface Frame {
    
    /**
     * 轨道类型枚举
     */
    enum TrackType {
        TrackInvalid(-1),
        TrackVideo(0),
        TrackAudio(1),
        TrackTitle(2),
        TrackApplication(3),
        TrackMax(4);
        
        private final int value;
        
        TrackType(int value) {
            this.value = value;
        }
        
        public int getValue() {
            return value;
        }
        
        public static TrackType fromValue(int value) {
            for (TrackType type : values()) {
                if (type.value == value) {
                    return type;
                }
            }
            return TrackInvalid;
        }
    }
    
    /**
     * 编解码器ID枚举
     */
    enum CodecId {
        CodecInvalid(-1),
        CodecH264(0),
        CodecH265(1),
        CodecAAC(2),
        CodecG711A(3),
        CodecG711U(4),
        CodecOpus(5),
        CodecL16(6),
        CodecVP8(7),
        CodecVP9(8),
        CodecAV1(9),
        CodecJPEG(10),
        CodecTS(11),
        CodecPS(12),
        CodecMax(13);
        
        private final int value;
        
        CodecId(int value) {
            this.value = value;
        }
        
        public int getValue() {
            return value;
        }
        
        public static CodecId fromValue(int value) {
            for (CodecId codec : values()) {
                if (codec.value == value) {
                    return codec;
                }
            }
            return CodecInvalid;
        }
    }
    
    /**
     * 获取解码时间戳（毫秒）
     * 
     * @return DTS
     */
    long getDts();
    
    /**
     * 获取显示时间戳（毫秒）
     * 
     * @return PTS
     */
    long getPts();
    
    /**
     * 获取帧数据
     * 
     * @return 帧数据字节数组
     */
    byte[] getData();
    
    /**
     * 获取帧数据大小
     * 
     * @return 数据大小
     */
    int getSize();
    
    /**
     * 获取编解码器ID
     * 
     * @return 编解码器ID
     */
    CodecId getCodecId();
    
    /**
     * 获取编解码器名称
     * 
     * @return 编解码器名称
     */
    String getCodecName();
    
    /**
     * 获取轨道类型
     * 
     * @return 轨道类型
     */
    TrackType getTrackType();
    
    /**
     * 是否为关键帧
     * 
     * @return boolean
     */
    boolean isKeyFrame();
    
    /**
     * 是否为配置帧（如SPS/PPS）
     * 
     * @return boolean
     */
    boolean isConfigFrame();
    
    /**
     * 获取轨道索引
     * 
     * @return 轨道索引
     */
    int getIndex();
    
    /**
     * 设置轨道索引
     * 
     * @param index 轨道索引
     */
    void setIndex(int index);
    
    /**
     * 获取前缀大小（如H264的起始码长度）
     * 
     * @return 前缀大小
     */
    int getPrefixSize();
    
    /**
     * 是否可缓存
     * 
     * @return boolean
     */
    boolean isCacheAble();
    
    /**
     * 创建可缓存的帧副本
     * 
     * @param frame 原始帧
     * @return 可缓存的帧
     */
    static Frame getCacheAbleFrame(Frame frame) {
        if (frame.isCacheAble()) {
            return frame;
        }
        return new FrameImpl(frame);
    }
    
    /**
     * 获取轨道类型对应的编解码器类型
     * 
     * @param codecId 编解码器ID
     * @return 轨道类型
     */
    static TrackType getTrackType(CodecId codecId) {
        switch (codecId) {
            case CodecH264:
            case CodecH265:
            case CodecVP8:
            case CodecVP9:
            case CodecAV1:
            case CodecJPEG:
                return TrackType.TrackVideo;
            case CodecAAC:
            case CodecG711A:
            case CodecG711U:
            case CodecOpus:
            case CodecL16:
                return TrackType.TrackAudio;
            default:
                return TrackType.TrackInvalid;
        }
    }
    
    /**
     * 获取编解码器名称
     * 
     * @param codecId 编解码器ID
     * @return 编解码器名称
     */
    static String getCodecName(CodecId codecId) {
        switch (codecId) {
            case CodecH264: return "H264";
            case CodecH265: return "H265";
            case CodecAAC: return "AAC";
            case CodecG711A: return "PCMA";
            case CodecG711U: return "PCMU";
            case CodecOpus: return "OPUS";
            case CodecL16: return "L16";
            case CodecVP8: return "VP8";
            case CodecVP9: return "VP9";
            case CodecAV1: return "AV1";
            case CodecJPEG: return "JPEG";
            case CodecTS: return "TS";
            case CodecPS: return "PS";
            default: return "UNKNOWN";
        }
    }
}
