package com.zlmediakit.gb28181.media;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Consumer;

/**
 * 媒体接收器实现类
 * 基于ZLMediaKit的MediaSink实现
 * 
 * <AUTHOR> Java Port
 */
public class MediaSinkImpl implements MediaSink {
    private static final Logger logger = LoggerFactory.getLogger(MediaSinkImpl.class);
    
    // 轨道管理
    private final ConcurrentHashMap<Frame.TrackType, Track> tracks = new ConcurrentHashMap<>();
    
    // 回调函数
    private Consumer<Frame> onFrameCallback;
    
    // 状态
    private boolean ready = false;
    
    // 统计信息
    private final AtomicLong totalFrames = new AtomicLong(0);
    private final AtomicLong totalBytes = new AtomicLong(0);
    private final AtomicLong droppedFrames = new AtomicLong(0);
    
    /**
     * 构造函数
     */
    public MediaSinkImpl() {
        logger.debug("Created MediaSinkImpl");
    }
    
    @Override
    public boolean inputFrame(Frame frame) {
        if (frame == null) {
            return false;
        }
        
        try {
            totalFrames.incrementAndGet();
            totalBytes.addAndGet(frame.getSize());
            
            // 获取对应的轨道
            Track track = getTrack(frame.getTrackType());
            if (track == null) {
                // 自动创建轨道
                track = createTrack(frame);
                if (track != null) {
                    addTrack(track);
                } else {
                    droppedFrames.incrementAndGet();
                    logger.warn("Failed to create track for frame: type={}, codec={}", 
                               frame.getTrackType(), frame.getCodecId());
                    return false;
                }
            }
            
            // 输入帧到轨道
            boolean success = track.inputFrame(frame);
            if (!success) {
                droppedFrames.incrementAndGet();
                return false;
            }
            
            // 调用回调
            if (onFrameCallback != null) {
                onFrameCallback.accept(frame);
            }
            
            // 更新就绪状态
            updateReadyState();
            
            return true;
            
        } catch (Exception e) {
            logger.error("Error processing frame in MediaSink", e);
            droppedFrames.incrementAndGet();
            return false;
        }
    }
    
    /**
     * 创建轨道
     * 
     * @param frame 帧
     * @return 轨道
     */
    private Track createTrack(Frame frame) {
        try {
            Frame.TrackType trackType = frame.getTrackType();
            Frame.CodecId codecId = frame.getCodecId();
            
            if (trackType == Frame.TrackType.TrackInvalid || 
                codecId == Frame.CodecId.CodecInvalid) {
                return null;
            }
            
            Track track = new TrackImpl(trackType, codecId);
            logger.debug("Auto-created track: type={}, codec={}", trackType, codecId);
            
            return track;
            
        } catch (Exception e) {
            logger.error("Error creating track", e);
            return null;
        }
    }
    
    /**
     * 更新就绪状态
     */
    private void updateReadyState() {
        // 如果有任何轨道就绪，则MediaSink就绪
        boolean newReady = tracks.values().stream().anyMatch(Track::isReady);
        
        if (newReady != ready) {
            ready = newReady;
            logger.debug("MediaSink ready state changed: {}", ready);
        }
    }
    
    @Override
    public boolean addTrack(Track track) {
        if (track == null) {
            return false;
        }
        
        try {
            Frame.TrackType trackType = track.getTrackType();
            
            // 检查是否已存在相同类型的轨道
            if (tracks.containsKey(trackType)) {
                logger.warn("Track type {} already exists", trackType);
                return false;
            }
            
            tracks.put(trackType, track);
            updateReadyState();
            
            logger.debug("Added track: type={}, codec={}, id={}", 
                        trackType, track.getCodecId(), track.getTrackId());
            
            return true;
            
        } catch (Exception e) {
            logger.error("Error adding track", e);
            return false;
        }
    }
    
    @Override
    public boolean removeTrack(Track track) {
        if (track == null) {
            return false;
        }
        
        try {
            Frame.TrackType trackType = track.getTrackType();
            Track removed = tracks.remove(trackType);
            
            if (removed != null) {
                updateReadyState();
                logger.debug("Removed track: type={}", trackType);
                return true;
            }
            
            return false;
            
        } catch (Exception e) {
            logger.error("Error removing track", e);
            return false;
        }
    }
    
    @Override
    public int getTrackCount() {
        return tracks.size();
    }
    
    @Override
    public Track getTrack(Frame.TrackType trackType) {
        return tracks.get(trackType);
    }
    
    @Override
    public void setOnFrame(Consumer<Frame> callback) {
        this.onFrameCallback = callback;
    }
    
    @Override
    public void flush() {
        try {
            // 刷新所有轨道
            tracks.values().forEach(track -> {
                try {
                    // 轨道本身没有flush方法，这里可以扩展
                    logger.debug("Flushing track: {}", track.getTrackId());
                } catch (Exception e) {
                    logger.error("Error flushing track", e);
                }
            });
            
        } catch (Exception e) {
            logger.error("Error flushing MediaSink", e);
        }
    }
    
    @Override
    public void reset() {
        try {
            // 清空轨道
            tracks.clear();
            
            // 重置状态
            ready = false;
            
            // 重置统计信息
            totalFrames.set(0);
            totalBytes.set(0);
            droppedFrames.set(0);
            
            logger.debug("MediaSink reset");
            
        } catch (Exception e) {
            logger.error("Error resetting MediaSink", e);
        }
    }
    
    @Override
    public String getStatistics() {
        StringBuilder sb = new StringBuilder();
        sb.append("MediaSink Statistics:\n");
        sb.append("  Total Frames: ").append(totalFrames.get()).append("\n");
        sb.append("  Total Bytes: ").append(totalBytes.get()).append("\n");
        sb.append("  Dropped Frames: ").append(droppedFrames.get()).append("\n");
        sb.append("  Track Count: ").append(tracks.size()).append("\n");
        sb.append("  Ready: ").append(ready).append("\n");
        
        // 添加各轨道统计
        tracks.forEach((trackType, track) -> {
            sb.append("\n").append(track.getStatistics());
        });
        
        return sb.toString();
    }
    
    @Override
    public boolean isReady() {
        return ready;
    }
    
    /**
     * 获取视频轨道
     * 
     * @return 视频轨道，如果不存在返回null
     */
    public Track getVideoTrack() {
        return getTrack(Frame.TrackType.TrackVideo);
    }
    
    /**
     * 获取音频轨道
     * 
     * @return 音频轨道，如果不存在返回null
     */
    public Track getAudioTrack() {
        return getTrack(Frame.TrackType.TrackAudio);
    }
    
    /**
     * 检查是否有视频轨道
     * 
     * @return boolean
     */
    public boolean hasVideo() {
        return getVideoTrack() != null;
    }
    
    /**
     * 检查是否有音频轨道
     * 
     * @return boolean
     */
    public boolean hasAudio() {
        return getAudioTrack() != null;
    }
    
    @Override
    public String toString() {
        return "MediaSinkImpl{" +
               "trackCount=" + tracks.size() +
               ", ready=" + ready +
               ", totalFrames=" + totalFrames.get() +
               ", totalBytes=" + totalBytes.get() +
               '}';
    }
}
