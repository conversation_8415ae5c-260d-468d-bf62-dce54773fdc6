package com.zlmediakit.gb28181.protocol;

import com.zlmediakit.gb28181.rtp.RtpPacket;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.ConcurrentSkipListMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Consumer;

/**
 * RTP接收器
 * 基于ZLMediaKit的RtpReceiver实现
 * 负责RTP包的排序和重组
 * 
 * <AUTHOR> Java Port
 */
public class RtpReceiver {
    private static final Logger logger = LoggerFactory.getLogger(RtpReceiver.class);
    
    // 配置参数
    private static final int MAX_SORT_SIZE = 50;      // 最大排序缓存大小
    private static final int CLEAR_COUNT = 100;       // 清理计数阈值
    private static final int MAX_DELAY_MS = 100;      // 最大延迟时间（毫秒）
    
    // 时钟频率
    private final int clockRate;
    
    // 回调函数
    private final Consumer<RtpPacket> onSorted;
    
    // 排序缓存
    private final ConcurrentSkipListMap<Integer, RtpPacket> sortedPackets = new ConcurrentSkipListMap<>();
    
    // 状态变量
    private final AtomicInteger nextSeq = new AtomicInteger(-1);
    private final AtomicLong lastSortTime = new AtomicLong(0);
    private final AtomicInteger clearCount = new AtomicInteger(0);
    
    // 统计信息
    private final AtomicLong totalPackets = new AtomicLong(0);
    private final AtomicLong lostPackets = new AtomicLong(0);
    private final AtomicLong duplicatePackets = new AtomicLong(0);
    private final AtomicLong outOfOrderPackets = new AtomicLong(0);
    
    /**
     * 构造函数
     * 
     * @param clockRate 时钟频率
     * @param onSorted 排序回调
     */
    public RtpReceiver(int clockRate, Consumer<RtpPacket> onSorted) {
        this.clockRate = clockRate;
        this.onSorted = onSorted;
    }
    
    /**
     * 输入RTP包
     * 
     * @param packet RTP包
     */
    public void inputRtp(RtpPacket packet) {
        if (packet == null) {
            return;
        }
        
        totalPackets.incrementAndGet();
        
        int seq = packet.getSequenceNumber();
        
        // 检查重复包
        if (sortedPackets.containsKey(seq)) {
            duplicatePackets.incrementAndGet();
            logger.debug("Duplicate RTP packet: seq={}", seq);
            return;
        }
        
        // 初始化序列号
        if (nextSeq.get() == -1) {
            nextSeq.set(seq);
            logger.debug("Initialize RTP sequence: {}", seq);
        }
        
        // 添加到排序缓存
        sortedPackets.put(seq, packet);
        
        // 检查是否乱序
        int expectedSeq = nextSeq.get();
        if (seq != expectedSeq) {
            outOfOrderPackets.incrementAndGet();
            logger.debug("Out of order RTP packet: expected={}, actual={}", expectedSeq, seq);
        }
        
        // 尝试输出连续的包
        tryFlushSorted();
        
        // 定期清理
        if (clearCount.incrementAndGet() >= CLEAR_COUNT) {
            clearCount.set(0);
            clearTimeout();
        }
    }
    
    /**
     * 尝试输出排序后的包
     */
    private void tryFlushSorted() {
        long currentTime = System.currentTimeMillis();
        lastSortTime.set(currentTime);
        
        while (!sortedPackets.isEmpty()) {
            int expectedSeq = nextSeq.get();
            RtpPacket packet = sortedPackets.get(expectedSeq);
            
            if (packet != null) {
                // 找到期望的包，输出它
                sortedPackets.remove(expectedSeq);
                nextSeq.set((expectedSeq + 1) & 0xFFFF);
                
                if (onSorted != null) {
                    onSorted.accept(packet);
                }
                
            } else {
                // 没有找到期望的包
                if (sortedPackets.size() >= MAX_SORT_SIZE) {
                    // 缓存满了，强制输出最小序列号的包
                    flushOldest();
                } else {
                    // 等待更多包
                    break;
                }
            }
        }
    }
    
    /**
     * 强制输出最旧的包
     */
    private void flushOldest() {
        if (sortedPackets.isEmpty()) {
            return;
        }
        
        // 获取最小序列号的包
        Integer minSeq = sortedPackets.firstKey();
        RtpPacket packet = sortedPackets.remove(minSeq);
        
        if (packet != null) {
            // 计算丢失的包数量
            int expectedSeq = nextSeq.get();
            int lostCount = calculateLostCount(expectedSeq, minSeq);
            
            if (lostCount > 0) {
                lostPackets.addAndGet(lostCount);
                logger.debug("Lost {} RTP packets: {} to {}", lostCount, expectedSeq, minSeq - 1);
            }
            
            // 更新下一个期望序列号
            nextSeq.set((minSeq + 1) & 0xFFFF);
            
            // 输出包
            if (onSorted != null) {
                onSorted.accept(packet);
            }
        }
    }
    
    /**
     * 计算丢失包数量
     * 
     * @param expectedSeq 期望序列号
     * @param actualSeq 实际序列号
     * @return 丢失包数量
     */
    private int calculateLostCount(int expectedSeq, int actualSeq) {
        int diff = actualSeq - expectedSeq;
        
        // 处理序列号回绕
        if (diff < 0) {
            diff += 65536;
        }
        
        // 限制最大丢失数量，避免序列号跳跃过大
        return Math.min(diff, 1000);
    }
    
    /**
     * 清理超时的包
     */
    private void clearTimeout() {
        if (sortedPackets.isEmpty()) {
            return;
        }
        
        long currentTime = System.currentTimeMillis();
        long maxDelayMs = MAX_DELAY_MS;
        
        // 检查是否有超时的包
        sortedPackets.entrySet().removeIf(entry -> {
            RtpPacket packet = entry.getValue();
            long delay = currentTime - packet.getReceiveTime();
            
            if (delay > maxDelayMs) {
                logger.debug("Remove timeout RTP packet: seq={}, delay={}ms", 
                           entry.getKey(), delay);
                
                // 输出超时的包
                if (onSorted != null) {
                    onSorted.accept(packet);
                }
                
                return true;
            }
            
            return false;
        });
    }
    
    /**
     * 刷新所有缓存的包
     */
    public void flush() {
        logger.debug("Flushing RTP receiver, {} packets in buffer", sortedPackets.size());
        
        // 按序列号顺序输出所有包
        while (!sortedPackets.isEmpty()) {
            Integer minSeq = sortedPackets.firstKey();
            RtpPacket packet = sortedPackets.remove(minSeq);
            
            if (packet != null && onSorted != null) {
                onSorted.accept(packet);
            }
        }
        
        // 重置状态
        nextSeq.set(-1);
        lastSortTime.set(0);
        clearCount.set(0);
    }
    
    /**
     * 获取统计信息
     * 
     * @return 统计信息字符串
     */
    public String getStatistics() {
        StringBuilder sb = new StringBuilder();
        sb.append("RtpReceiver Statistics:\n");
        sb.append("  Total Packets: ").append(totalPackets.get()).append("\n");
        sb.append("  Lost Packets: ").append(lostPackets.get()).append("\n");
        sb.append("  Duplicate Packets: ").append(duplicatePackets.get()).append("\n");
        sb.append("  Out of Order Packets: ").append(outOfOrderPackets.get()).append("\n");
        sb.append("  Buffer Size: ").append(sortedPackets.size()).append("\n");
        sb.append("  Next Expected Seq: ").append(nextSeq.get()).append("\n");
        sb.append("  Clock Rate: ").append(clockRate).append("\n");
        
        // 计算丢包率
        long total = totalPackets.get();
        if (total > 0) {
            double lossRate = (double) lostPackets.get() / total * 100;
            sb.append("  Loss Rate: ").append(String.format("%.2f%%", lossRate)).append("\n");
        }
        
        return sb.toString();
    }
    
    // Getter方法
    
    public int getClockRate() {
        return clockRate;
    }
    
    public int getBufferSize() {
        return sortedPackets.size();
    }
    
    public int getNextSeq() {
        return nextSeq.get();
    }
    
    public long getTotalPackets() {
        return totalPackets.get();
    }
    
    public long getLostPackets() {
        return lostPackets.get();
    }
    
    public long getDuplicatePackets() {
        return duplicatePackets.get();
    }
    
    public long getOutOfOrderPackets() {
        return outOfOrderPackets.get();
    }
}
