package com.zlmediakit.gb28181.media;

import java.util.Arrays;

/**
 * 媒体帧实现类
 * 基于ZLMediaKit的Frame实现
 * 
 * <AUTHOR> Java Port
 */
public class FrameImpl implements Frame {
    private long dts;
    private long pts;
    private byte[] data;
    private CodecId codecId;
    private TrackType trackType;
    private boolean keyFrame;
    private boolean configFrame;
    private int index;
    private int prefixSize;
    
    /**
     * 默认构造函数
     */
    public FrameImpl() {
        this.codecId = CodecId.CodecInvalid;
        this.trackType = TrackType.TrackInvalid;
    }
    
    /**
     * 构造函数
     * 
     * @param codecId 编解码器ID
     * @param data 帧数据
     * @param dts 解码时间戳
     * @param pts 显示时间戳
     */
    public FrameImpl(CodecId codecId, byte[] data, long dts, long pts) {
        this.codecId = codecId;
        this.data = data != null ? Arrays.copyOf(data, data.length) : new byte[0];
        this.dts = dts;
        this.pts = pts;
        this.trackType = Frame.getTrackType(codecId);
    }
    
    /**
     * 拷贝构造函数
     * 
     * @param frame 原始帧
     */
    public FrameImpl(Frame frame) {
        this.dts = frame.getDts();
        this.pts = frame.getPts();
        this.data = Arrays.copyOf(frame.getData(), frame.getSize());
        this.codecId = frame.getCodecId();
        this.trackType = frame.getTrackType();
        this.keyFrame = frame.isKeyFrame();
        this.configFrame = frame.isConfigFrame();
        this.index = frame.getIndex();
        this.prefixSize = frame.getPrefixSize();
    }
    
    @Override
    public long getDts() {
        return dts;
    }
    
    public void setDts(long dts) {
        this.dts = dts;
    }
    
    @Override
    public long getPts() {
        return pts;
    }
    
    public void setPts(long pts) {
        this.pts = pts;
    }
    
    @Override
    public byte[] getData() {
        return data;
    }
    
    public void setData(byte[] data) {
        this.data = data != null ? Arrays.copyOf(data, data.length) : new byte[0];
    }
    
    @Override
    public int getSize() {
        return data != null ? data.length : 0;
    }
    
    @Override
    public CodecId getCodecId() {
        return codecId;
    }
    
    public void setCodecId(CodecId codecId) {
        this.codecId = codecId;
        this.trackType = Frame.getTrackType(codecId);
    }
    
    @Override
    public String getCodecName() {
        return Frame.getCodecName(codecId);
    }
    
    @Override
    public TrackType getTrackType() {
        return trackType;
    }
    
    public void setTrackType(TrackType trackType) {
        this.trackType = trackType;
    }
    
    @Override
    public boolean isKeyFrame() {
        return keyFrame;
    }
    
    public void setKeyFrame(boolean keyFrame) {
        this.keyFrame = keyFrame;
    }
    
    @Override
    public boolean isConfigFrame() {
        return configFrame;
    }
    
    public void setConfigFrame(boolean configFrame) {
        this.configFrame = configFrame;
    }
    
    @Override
    public int getIndex() {
        return index;
    }
    
    @Override
    public void setIndex(int index) {
        this.index = index;
    }
    
    @Override
    public int getPrefixSize() {
        return prefixSize;
    }
    
    public void setPrefixSize(int prefixSize) {
        this.prefixSize = prefixSize;
    }
    
    @Override
    public boolean isCacheAble() {
        return true; // FrameImpl总是可缓存的
    }
    
    /**
     * 检查是否为H264关键帧
     * 
     * @return boolean
     */
    public boolean isH264KeyFrame() {
        if (codecId != CodecId.CodecH264 || data == null || data.length < 5) {
            return false;
        }
        
        // 查找NALU类型
        for (int i = 0; i < data.length - 4; i++) {
            if (data[i] == 0x00 && data[i + 1] == 0x00 && 
                data[i + 2] == 0x00 && data[i + 3] == 0x01) {
                int naluType = data[i + 4] & 0x1F;
                return naluType == 5; // IDR帧
            } else if (data[i] == 0x00 && data[i + 1] == 0x00 && data[i + 2] == 0x01) {
                int naluType = data[i + 3] & 0x1F;
                return naluType == 5; // IDR帧
            }
        }
        
        return false;
    }
    
    /**
     * 检查是否为H265关键帧
     * 
     * @return boolean
     */
    public boolean isH265KeyFrame() {
        if (codecId != CodecId.CodecH265 || data == null || data.length < 5) {
            return false;
        }
        
        // 查找NALU类型
        for (int i = 0; i < data.length - 4; i++) {
            if (data[i] == 0x00 && data[i + 1] == 0x00 && 
                data[i + 2] == 0x00 && data[i + 3] == 0x01) {
                int naluType = (data[i + 4] & 0x7E) >> 1;
                return naluType >= 16 && naluType <= 23; // IDR/CRA帧
            } else if (data[i] == 0x00 && data[i + 1] == 0x00 && data[i + 2] == 0x01) {
                int naluType = (data[i + 3] & 0x7E) >> 1;
                return naluType >= 16 && naluType <= 23; // IDR/CRA帧
            }
        }
        
        return false;
    }
    
    /**
     * 自动检测关键帧和配置帧
     */
    public void autoDetectKeyFrame() {
        switch (codecId) {
            case CodecH264:
                this.keyFrame = isH264KeyFrame();
                this.configFrame = isH264ConfigFrame();
                break;
            case CodecH265:
                this.keyFrame = isH265KeyFrame();
                this.configFrame = isH265ConfigFrame();
                break;
            default:
                this.keyFrame = false;
                this.configFrame = false;
                break;
        }
    }

    /**
     * 检查是否为H264配置帧 (SPS/PPS)
     *
     * @return boolean
     */
    public boolean isH264ConfigFrame() {
        if (codecId != CodecId.CodecH264 || data == null || data.length < 5) {
            return false;
        }

        // 查找NALU类型
        for (int i = 0; i < data.length - 4; i++) {
            if (data[i] == 0x00 && data[i + 1] == 0x00 &&
                data[i + 2] == 0x00 && data[i + 3] == 0x01) {
                int naluType = data[i + 4] & 0x1F;
                return naluType == 7 || naluType == 8; // SPS(7) 或 PPS(8)
            } else if (data[i] == 0x00 && data[i + 1] == 0x00 && data[i + 2] == 0x01) {
                int naluType = data[i + 3] & 0x1F;
                return naluType == 7 || naluType == 8; // SPS(7) 或 PPS(8)
            }
        }

        return false;
    }

    /**
     * 检查是否为H265配置帧 (VPS/SPS/PPS)
     *
     * @return boolean
     */
    public boolean isH265ConfigFrame() {
        if (codecId != CodecId.CodecH265 || data == null || data.length < 5) {
            return false;
        }

        // 查找NALU类型
        for (int i = 0; i < data.length - 4; i++) {
            if (data[i] == 0x00 && data[i + 1] == 0x00 &&
                data[i + 2] == 0x00 && data[i + 3] == 0x01) {
                int naluType = (data[i + 4] & 0x7E) >> 1;
                return naluType == 32 || naluType == 33 || naluType == 34; // VPS(32), SPS(33), PPS(34)
            } else if (data[i] == 0x00 && data[i + 1] == 0x00 && data[i + 2] == 0x01) {
                int naluType = (data[i + 3] & 0x7E) >> 1;
                return naluType == 32 || naluType == 33 || naluType == 34; // VPS(32), SPS(33), PPS(34)
            }
        }

        return false;
    }

    @Override
    public String toString() {
        return "FrameImpl{" +
               "dts=" + dts +
               ", pts=" + pts +
               ", size=" + getSize() +
               ", codecId=" + codecId +
               ", trackType=" + trackType +
               ", keyFrame=" + keyFrame +
               ", configFrame=" + configFrame +
               ", index=" + index +
               '}';
    }
}
