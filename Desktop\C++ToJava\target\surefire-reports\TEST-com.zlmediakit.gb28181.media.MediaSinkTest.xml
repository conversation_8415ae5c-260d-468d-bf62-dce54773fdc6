<?xml version="1.0" encoding="UTF-8"?>
<testsuite xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="https://maven.apache.org/surefire/maven-surefire-plugin/xsd/surefire-test-report-3.0.xsd" version="3.0" name="com.zlmediakit.gb28181.media.MediaSinkTest" time="0.056" tests="12" errors="0" skipped="0" failures="0">
  <properties>
    <property name="java.specification.version" value="17"/>
    <property name="sun.cpu.isalist" value="amd64"/>
    <property name="sun.jnu.encoding" value="GBK"/>
    <property name="java.class.path" value="C:\Users\<USER>\Desktop\ZLMediaKit\C++ToJava\target\test-classes;C:\Users\<USER>\Desktop\ZLMediaKit\C++ToJava\target\classes;C:\Users\<USER>\.m2\repository\io\netty\netty-all\4.1.92.Final\netty-all-4.1.92.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-buffer\4.1.92.Final\netty-buffer-4.1.92.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec\4.1.92.Final\netty-codec-4.1.92.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-dns\4.1.92.Final\netty-codec-dns-4.1.92.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-haproxy\4.1.92.Final\netty-codec-haproxy-4.1.92.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-http\4.1.92.Final\netty-codec-http-4.1.92.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-http2\4.1.92.Final\netty-codec-http2-4.1.92.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-memcache\4.1.92.Final\netty-codec-memcache-4.1.92.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-mqtt\4.1.92.Final\netty-codec-mqtt-4.1.92.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-redis\4.1.92.Final\netty-codec-redis-4.1.92.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-smtp\4.1.92.Final\netty-codec-smtp-4.1.92.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-socks\4.1.92.Final\netty-codec-socks-4.1.92.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-stomp\4.1.92.Final\netty-codec-stomp-4.1.92.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-xml\4.1.92.Final\netty-codec-xml-4.1.92.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-common\4.1.92.Final\netty-common-4.1.92.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-handler\4.1.92.Final\netty-handler-4.1.92.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport-native-unix-common\4.1.92.Final\netty-transport-native-unix-common-4.1.92.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-handler-proxy\4.1.92.Final\netty-handler-proxy-4.1.92.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-handler-ssl-ocsp\4.1.92.Final\netty-handler-ssl-ocsp-4.1.92.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-resolver\4.1.92.Final\netty-resolver-4.1.92.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-resolver-dns\4.1.92.Final\netty-resolver-dns-4.1.92.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport\4.1.92.Final\netty-transport-4.1.92.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport-rxtx\4.1.92.Final\netty-transport-rxtx-4.1.92.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport-sctp\4.1.92.Final\netty-transport-sctp-4.1.92.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport-udt\4.1.92.Final\netty-transport-udt-4.1.92.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport-classes-epoll\4.1.92.Final\netty-transport-classes-epoll-4.1.92.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport-classes-kqueue\4.1.92.Final\netty-transport-classes-kqueue-4.1.92.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-resolver-dns-classes-macos\4.1.92.Final\netty-resolver-dns-classes-macos-4.1.92.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport-native-epoll\4.1.92.Final\netty-transport-native-epoll-4.1.92.Final-linux-x86_64.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport-native-epoll\4.1.92.Final\netty-transport-native-epoll-4.1.92.Final-linux-aarch_64.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport-native-kqueue\4.1.92.Final\netty-transport-native-kqueue-4.1.92.Final-osx-x86_64.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport-native-kqueue\4.1.92.Final\netty-transport-native-kqueue-4.1.92.Final-osx-aarch_64.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-resolver-dns-native-macos\4.1.92.Final\netty-resolver-dns-native-macos-4.1.92.Final-osx-x86_64.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-resolver-dns-native-macos\4.1.92.Final\netty-resolver-dns-native-macos-4.1.92.Final-osx-aarch_64.jar;C:\Users\<USER>\.m2\repository\org\slf4j\slf4j-api\2.0.7\slf4j-api-2.0.7.jar;C:\Users\<USER>\.m2\repository\ch\qos\logback\logback-classic\1.4.7\logback-classic-1.4.7.jar;C:\Users\<USER>\.m2\repository\ch\qos\logback\logback-core\1.4.7\logback-core-1.4.7.jar;C:\Users\<USER>\.m2\repository\com\google\guava\guava\31.1-jre\guava-31.1-jre.jar;C:\Users\<USER>\.m2\repository\com\google\guava\failureaccess\1.0.1\failureaccess-1.0.1.jar;C:\Users\<USER>\.m2\repository\com\google\guava\listenablefuture\9999.0-empty-to-avoid-conflict-with-guava\listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar;C:\Users\<USER>\.m2\repository\com\google\code\findbugs\jsr305\3.0.2\jsr305-3.0.2.jar;C:\Users\<USER>\.m2\repository\org\checkerframework\checker-qual\3.12.0\checker-qual-3.12.0.jar;C:\Users\<USER>\.m2\repository\com\google\errorprone\error_prone_annotations\2.11.0\error_prone_annotations-2.11.0.jar;C:\Users\<USER>\.m2\repository\com\google\j2objc\j2objc-annotations\1.3\j2objc-annotations-1.3.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-databind\2.15.2\jackson-databind-2.15.2.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-annotations\2.15.2\jackson-annotations-2.15.2.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-core\2.15.2\jackson-core-2.15.2.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-configuration2\2.9.0\commons-configuration2-2.9.0.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-lang3\3.12.0\commons-lang3-3.12.0.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-text\1.10.0\commons-text-1.10.0.jar;C:\Users\<USER>\.m2\repository\commons-logging\commons-logging\1.2\commons-logging-1.2.jar;C:\Users\<USER>\.m2\repository\commons-beanutils\commons-beanutils\1.9.4\commons-beanutils-1.9.4.jar;C:\Users\<USER>\.m2\repository\commons-collections\commons-collections\3.2.2\commons-collections-3.2.2.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter\5.9.2\junit-jupiter-5.9.2.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-api\5.9.2\junit-jupiter-api-5.9.2.jar;C:\Users\<USER>\.m2\repository\org\opentest4j\opentest4j\1.2.0\opentest4j-1.2.0.jar;C:\Users\<USER>\.m2\repository\org\junit\platform\junit-platform-commons\1.9.2\junit-platform-commons-1.9.2.jar;C:\Users\<USER>\.m2\repository\org\apiguardian\apiguardian-api\1.1.2\apiguardian-api-1.1.2.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-params\5.9.2\junit-jupiter-params-5.9.2.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-engine\5.9.2\junit-jupiter-engine-5.9.2.jar;C:\Users\<USER>\.m2\repository\org\junit\platform\junit-platform-engine\1.9.2\junit-platform-engine-1.9.2.jar;C:\Users\<USER>\.m2\repository\org\mockito\mockito-core\5.3.1\mockito-core-5.3.1.jar;C:\Users\<USER>\.m2\repository\net\bytebuddy\byte-buddy\1.14.4\byte-buddy-1.14.4.jar;C:\Users\<USER>\.m2\repository\net\bytebuddy\byte-buddy-agent\1.14.4\byte-buddy-agent-1.14.4.jar;C:\Users\<USER>\.m2\repository\org\objenesis\objenesis\3.3\objenesis-3.3.jar;C:\Users\<USER>\.m2\repository\org\mockito\mockito-junit-jupiter\5.3.1\mockito-junit-jupiter-5.3.1.jar;"/>
    <property name="java.vm.vendor" value="Oracle Corporation"/>
    <property name="sun.arch.data.model" value="64"/>
    <property name="user.variant" value=""/>
    <property name="java.vendor.url" value="https://java.oracle.com/"/>
    <property name="user.timezone" value="Asia/Shanghai"/>
    <property name="os.name" value="Windows 11"/>
    <property name="java.vm.specification.version" value="17"/>
    <property name="sun.java.launcher" value="SUN_STANDARD"/>
    <property name="user.country" value="CN"/>
    <property name="sun.boot.library.path" value="C:\Program Files\Java\jdk-17.0.2\bin"/>
    <property name="sun.java.command" value="C:\Users\<USER>\AppData\Local\Temp\surefire3822197451254693627\surefirebooter-20250801181955480_3.jar C:\Users\<USER>\AppData\Local\Temp\surefire3822197451254693627 2025-08-01T18-19-55_323-jvmRun1 surefire-20250801181955480_1tmp surefire_0-20250801181955480_2tmp"/>
    <property name="jdk.debug" value="release"/>
    <property name="surefire.test.class.path" value="C:\Users\<USER>\Desktop\ZLMediaKit\C++ToJava\target\test-classes;C:\Users\<USER>\Desktop\ZLMediaKit\C++ToJava\target\classes;C:\Users\<USER>\.m2\repository\io\netty\netty-all\4.1.92.Final\netty-all-4.1.92.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-buffer\4.1.92.Final\netty-buffer-4.1.92.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec\4.1.92.Final\netty-codec-4.1.92.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-dns\4.1.92.Final\netty-codec-dns-4.1.92.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-haproxy\4.1.92.Final\netty-codec-haproxy-4.1.92.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-http\4.1.92.Final\netty-codec-http-4.1.92.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-http2\4.1.92.Final\netty-codec-http2-4.1.92.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-memcache\4.1.92.Final\netty-codec-memcache-4.1.92.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-mqtt\4.1.92.Final\netty-codec-mqtt-4.1.92.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-redis\4.1.92.Final\netty-codec-redis-4.1.92.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-smtp\4.1.92.Final\netty-codec-smtp-4.1.92.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-socks\4.1.92.Final\netty-codec-socks-4.1.92.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-stomp\4.1.92.Final\netty-codec-stomp-4.1.92.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-xml\4.1.92.Final\netty-codec-xml-4.1.92.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-common\4.1.92.Final\netty-common-4.1.92.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-handler\4.1.92.Final\netty-handler-4.1.92.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport-native-unix-common\4.1.92.Final\netty-transport-native-unix-common-4.1.92.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-handler-proxy\4.1.92.Final\netty-handler-proxy-4.1.92.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-handler-ssl-ocsp\4.1.92.Final\netty-handler-ssl-ocsp-4.1.92.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-resolver\4.1.92.Final\netty-resolver-4.1.92.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-resolver-dns\4.1.92.Final\netty-resolver-dns-4.1.92.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport\4.1.92.Final\netty-transport-4.1.92.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport-rxtx\4.1.92.Final\netty-transport-rxtx-4.1.92.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport-sctp\4.1.92.Final\netty-transport-sctp-4.1.92.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport-udt\4.1.92.Final\netty-transport-udt-4.1.92.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport-classes-epoll\4.1.92.Final\netty-transport-classes-epoll-4.1.92.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport-classes-kqueue\4.1.92.Final\netty-transport-classes-kqueue-4.1.92.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-resolver-dns-classes-macos\4.1.92.Final\netty-resolver-dns-classes-macos-4.1.92.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport-native-epoll\4.1.92.Final\netty-transport-native-epoll-4.1.92.Final-linux-x86_64.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport-native-epoll\4.1.92.Final\netty-transport-native-epoll-4.1.92.Final-linux-aarch_64.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport-native-kqueue\4.1.92.Final\netty-transport-native-kqueue-4.1.92.Final-osx-x86_64.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport-native-kqueue\4.1.92.Final\netty-transport-native-kqueue-4.1.92.Final-osx-aarch_64.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-resolver-dns-native-macos\4.1.92.Final\netty-resolver-dns-native-macos-4.1.92.Final-osx-x86_64.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-resolver-dns-native-macos\4.1.92.Final\netty-resolver-dns-native-macos-4.1.92.Final-osx-aarch_64.jar;C:\Users\<USER>\.m2\repository\org\slf4j\slf4j-api\2.0.7\slf4j-api-2.0.7.jar;C:\Users\<USER>\.m2\repository\ch\qos\logback\logback-classic\1.4.7\logback-classic-1.4.7.jar;C:\Users\<USER>\.m2\repository\ch\qos\logback\logback-core\1.4.7\logback-core-1.4.7.jar;C:\Users\<USER>\.m2\repository\com\google\guava\guava\31.1-jre\guava-31.1-jre.jar;C:\Users\<USER>\.m2\repository\com\google\guava\failureaccess\1.0.1\failureaccess-1.0.1.jar;C:\Users\<USER>\.m2\repository\com\google\guava\listenablefuture\9999.0-empty-to-avoid-conflict-with-guava\listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar;C:\Users\<USER>\.m2\repository\com\google\code\findbugs\jsr305\3.0.2\jsr305-3.0.2.jar;C:\Users\<USER>\.m2\repository\org\checkerframework\checker-qual\3.12.0\checker-qual-3.12.0.jar;C:\Users\<USER>\.m2\repository\com\google\errorprone\error_prone_annotations\2.11.0\error_prone_annotations-2.11.0.jar;C:\Users\<USER>\.m2\repository\com\google\j2objc\j2objc-annotations\1.3\j2objc-annotations-1.3.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-databind\2.15.2\jackson-databind-2.15.2.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-annotations\2.15.2\jackson-annotations-2.15.2.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-core\2.15.2\jackson-core-2.15.2.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-configuration2\2.9.0\commons-configuration2-2.9.0.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-lang3\3.12.0\commons-lang3-3.12.0.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-text\1.10.0\commons-text-1.10.0.jar;C:\Users\<USER>\.m2\repository\commons-logging\commons-logging\1.2\commons-logging-1.2.jar;C:\Users\<USER>\.m2\repository\commons-beanutils\commons-beanutils\1.9.4\commons-beanutils-1.9.4.jar;C:\Users\<USER>\.m2\repository\commons-collections\commons-collections\3.2.2\commons-collections-3.2.2.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter\5.9.2\junit-jupiter-5.9.2.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-api\5.9.2\junit-jupiter-api-5.9.2.jar;C:\Users\<USER>\.m2\repository\org\opentest4j\opentest4j\1.2.0\opentest4j-1.2.0.jar;C:\Users\<USER>\.m2\repository\org\junit\platform\junit-platform-commons\1.9.2\junit-platform-commons-1.9.2.jar;C:\Users\<USER>\.m2\repository\org\apiguardian\apiguardian-api\1.1.2\apiguardian-api-1.1.2.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-params\5.9.2\junit-jupiter-params-5.9.2.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-engine\5.9.2\junit-jupiter-engine-5.9.2.jar;C:\Users\<USER>\.m2\repository\org\junit\platform\junit-platform-engine\1.9.2\junit-platform-engine-1.9.2.jar;C:\Users\<USER>\.m2\repository\org\mockito\mockito-core\5.3.1\mockito-core-5.3.1.jar;C:\Users\<USER>\.m2\repository\net\bytebuddy\byte-buddy\1.14.4\byte-buddy-1.14.4.jar;C:\Users\<USER>\.m2\repository\net\bytebuddy\byte-buddy-agent\1.14.4\byte-buddy-agent-1.14.4.jar;C:\Users\<USER>\.m2\repository\org\objenesis\objenesis\3.3\objenesis-3.3.jar;C:\Users\<USER>\.m2\repository\org\mockito\mockito-junit-jupiter\5.3.1\mockito-junit-jupiter-5.3.1.jar;"/>
    <property name="sun.cpu.endian" value="little"/>
    <property name="user.home" value="C:\Users\<USER>\Program Files\Java\jdk-17.0.2"/>
    <property name="file.separator" value="\"/>
    <property name="basedir" value="C:\Users\<USER>\Desktop\ZLMediaKit\C++ToJava"/>
    <property name="java.vm.compressedOopsMode" value="Zero based"/>
    <property name="line.separator" value="&#10;"/>
    <property name="java.vm.specification.vendor" value="Oracle Corporation"/>
    <property name="java.specification.name" value="Java Platform API Specification"/>
    <property name="surefire.real.class.path" value="C:\Users\<USER>\AppData\Local\Temp\surefire3822197451254693627\surefirebooter-20250801181955480_3.jar"/>
    <property name="user.script" value=""/>
    <property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers"/>
    <property name="java.runtime.version" value="17.0.2+8-LTS-86"/>
    <property name="user.name" value="yy"/>
    <property name="path.separator" value=";"/>
    <property name="os.version" value="10.0"/>
    <property name="java.runtime.name" value="Java(TM) SE Runtime Environment"/>
    <property name="file.encoding" value="GBK"/>
    <property name="java.vm.name" value="Java HotSpot(TM) 64-Bit Server VM"/>
    <property name="localRepository" value="C:\Users\<USER>\.m2\repository"/>
    <property name="java.vendor.url.bug" value="https://bugreport.java.com/bugreport/"/>
    <property name="java.io.tmpdir" value="C:\Users\<USER>\AppData\Local\Temp\"/>
    <property name="java.version" value="17.0.2"/>
    <property name="user.dir" value="C:\Users\<USER>\Desktop\ZLMediaKit\C++ToJava"/>
    <property name="os.arch" value="amd64"/>
    <property name="java.vm.specification.name" value="Java Virtual Machine Specification"/>
    <property name="sun.os.patch.level" value=""/>
    <property name="native.encoding" value="GBK"/>
    <property name="java.library.path" value="C:\Program Files\Java\jdk-17.0.2\bin;C:\Windows\Sun\Java\bin;C:\Windows\system32;C:\Windows;D:\Anaconda3\condabin;C:\Program Files\PowerShell\7;d:\Users\yy\AppData\Local\Programs\cursor\resources\app\bin;C:\Program Files\MySQL\MySQL Server 8.3\bin;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\NVIDIA Corporation\NVIDIA NvDLISR;C:\Program Files\dotnet\;C:\Program Files\nodejs\;C:\Program Files\Java\jdk-17.0.2\bin\;D:\Anaconda3\envs\2316406040\;C:\Program Files\PowerShell\7\;C:\Program Files\chrome-win64\;C:\Program Files\ffmpeg\bin;D:\MATLAB\R2024a\runtime\win64;D:\MATLAB\R2024a\bin;D:\Topaz\bin\;D:\ollama\;C:\Users\<USER>\.local\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Roaming\npm;D:\ollama;d:\Users\yy\AppData\Local\Programs\cursor\resources\app\bin;C:\Program Files\Cognex\VisionPro\bin;C:\Program Files\Common Files\Pleora\eBUS SDK;D:\Program Files\CMake\bin;D:\Program Files\Git\cmd;D:\apache-maven-3.9.10\bin;C:\Users\<USER>\.local\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Roaming\npm;D:\ollama;D:\Users\yy\AppData\Local\Programs\cursor\resources\app\bin;D:\Users\yy\AppData\Local\Programs\Microsoft VS Code\bin;D:\Program Files\JetBrains\IntelliJ IDEA 2024.1\bin;;."/>
    <property name="java.vm.info" value="mixed mode, sharing"/>
    <property name="java.vendor" value="Oracle Corporation"/>
    <property name="java.vm.version" value="17.0.2+8-LTS-86"/>
    <property name="sun.io.unicode.encoding" value="UnicodeLittle"/>
    <property name="java.class.version" value="61.0"/>
  </properties>
  <testcase name="testReset" classname="com.zlmediakit.gb28181.media.MediaSinkTest" time="0.003">
    <system-out><![CDATA[18:19:56.069 [main] DEBUG com.zlmediakit.gb28181.media.MediaSinkImpl -- Created MediaSinkImpl
18:19:56.071 [main] DEBUG com.zlmediakit.gb28181.media.TrackImpl -- Created track: type=TrackVideo, codec=CodecH264
18:19:56.071 [main] DEBUG com.zlmediakit.gb28181.media.MediaSinkImpl -- Auto-created track: type=TrackVideo, codec=CodecH264
18:19:56.072 [main] DEBUG com.zlmediakit.gb28181.media.MediaSinkImpl -- Added track: type=TrackVideo, codec=CodecH264, id=1
18:19:56.072 [main] DEBUG com.zlmediakit.gb28181.media.MediaSinkImpl -- MediaSink ready state changed: true
18:19:56.072 [main] DEBUG com.zlmediakit.gb28181.media.TrackImpl -- Created track: type=TrackAudio, codec=CodecAAC
18:19:56.072 [main] DEBUG com.zlmediakit.gb28181.media.MediaSinkImpl -- Auto-created track: type=TrackAudio, codec=CodecAAC
18:19:56.072 [main] DEBUG com.zlmediakit.gb28181.media.MediaSinkImpl -- Added track: type=TrackAudio, codec=CodecAAC, id=2
18:19:56.072 [main] DEBUG com.zlmediakit.gb28181.media.MediaSinkImpl -- MediaSink reset
]]></system-out>
  </testcase>
  <testcase name="testGetTrackByType" classname="com.zlmediakit.gb28181.media.MediaSinkTest" time="0.001">
    <system-out><![CDATA[18:19:56.073 [main] DEBUG com.zlmediakit.gb28181.media.MediaSinkImpl -- Created MediaSinkImpl
18:19:56.073 [main] DEBUG com.zlmediakit.gb28181.media.TrackImpl -- Created track: type=TrackVideo, codec=CodecH264
18:19:56.073 [main] DEBUG com.zlmediakit.gb28181.media.MediaSinkImpl -- Auto-created track: type=TrackVideo, codec=CodecH264
18:19:56.073 [main] DEBUG com.zlmediakit.gb28181.media.MediaSinkImpl -- Added track: type=TrackVideo, codec=CodecH264, id=1
18:19:56.073 [main] DEBUG com.zlmediakit.gb28181.media.MediaSinkImpl -- MediaSink ready state changed: true
18:19:56.073 [main] DEBUG com.zlmediakit.gb28181.media.TrackImpl -- Created track: type=TrackAudio, codec=CodecAAC
18:19:56.073 [main] DEBUG com.zlmediakit.gb28181.media.MediaSinkImpl -- Auto-created track: type=TrackAudio, codec=CodecAAC
18:19:56.073 [main] DEBUG com.zlmediakit.gb28181.media.MediaSinkImpl -- Added track: type=TrackAudio, codec=CodecAAC, id=2
]]></system-out>
  </testcase>
  <testcase name="testToString" classname="com.zlmediakit.gb28181.media.MediaSinkTest" time="0.005">
    <system-out><![CDATA[18:19:56.074 [main] DEBUG com.zlmediakit.gb28181.media.MediaSinkImpl -- Created MediaSinkImpl
]]></system-out>
  </testcase>
  <testcase name="testMultipleCodecs" classname="com.zlmediakit.gb28181.media.MediaSinkTest" time="0.002">
    <system-out><![CDATA[18:19:56.079 [main] DEBUG com.zlmediakit.gb28181.media.MediaSinkImpl -- Created MediaSinkImpl
18:19:56.079 [main] DEBUG com.zlmediakit.gb28181.media.TrackImpl -- Created track: type=TrackVideo, codec=CodecH264
18:19:56.079 [main] DEBUG com.zlmediakit.gb28181.media.MediaSinkImpl -- Auto-created track: type=TrackVideo, codec=CodecH264
18:19:56.079 [main] DEBUG com.zlmediakit.gb28181.media.MediaSinkImpl -- Added track: type=TrackVideo, codec=CodecH264, id=1
18:19:56.079 [main] DEBUG com.zlmediakit.gb28181.media.MediaSinkImpl -- MediaSink ready state changed: true
18:19:56.080 [main] DEBUG com.zlmediakit.gb28181.media.TrackImpl -- Created track: type=TrackAudio, codec=CodecAAC
18:19:56.080 [main] DEBUG com.zlmediakit.gb28181.media.MediaSinkImpl -- Auto-created track: type=TrackAudio, codec=CodecAAC
18:19:56.080 [main] DEBUG com.zlmediakit.gb28181.media.MediaSinkImpl -- Added track: type=TrackAudio, codec=CodecAAC, id=2
]]></system-out>
  </testcase>
  <testcase name="testGetTrackByCodec" classname="com.zlmediakit.gb28181.media.MediaSinkTest" time="0.001">
    <system-out><![CDATA[18:19:56.081 [main] DEBUG com.zlmediakit.gb28181.media.MediaSinkImpl -- Created MediaSinkImpl
18:19:56.081 [main] DEBUG com.zlmediakit.gb28181.media.TrackImpl -- Created track: type=TrackVideo, codec=CodecH264
18:19:56.081 [main] DEBUG com.zlmediakit.gb28181.media.MediaSinkImpl -- Auto-created track: type=TrackVideo, codec=CodecH264
18:19:56.081 [main] DEBUG com.zlmediakit.gb28181.media.MediaSinkImpl -- Added track: type=TrackVideo, codec=CodecH264, id=1
18:19:56.081 [main] DEBUG com.zlmediakit.gb28181.media.MediaSinkImpl -- MediaSink ready state changed: true
18:19:56.081 [main] DEBUG com.zlmediakit.gb28181.media.TrackImpl -- Created track: type=TrackAudio, codec=CodecAAC
18:19:56.081 [main] DEBUG com.zlmediakit.gb28181.media.MediaSinkImpl -- Auto-created track: type=TrackAudio, codec=CodecAAC
18:19:56.081 [main] DEBUG com.zlmediakit.gb28181.media.MediaSinkImpl -- Added track: type=TrackAudio, codec=CodecAAC, id=2
]]></system-out>
  </testcase>
  <testcase name="testBasicProperties" classname="com.zlmediakit.gb28181.media.MediaSinkTest" time="0.001">
    <system-out><![CDATA[18:19:56.082 [main] DEBUG com.zlmediakit.gb28181.media.MediaSinkImpl -- Created MediaSinkImpl
]]></system-out>
  </testcase>
  <testcase name="testInputFrame" classname="com.zlmediakit.gb28181.media.MediaSinkTest" time="0.001">
    <system-out><![CDATA[18:19:56.083 [main] DEBUG com.zlmediakit.gb28181.media.MediaSinkImpl -- Created MediaSinkImpl
18:19:56.083 [main] DEBUG com.zlmediakit.gb28181.media.TrackImpl -- Created track: type=TrackVideo, codec=CodecH264
18:19:56.083 [main] DEBUG com.zlmediakit.gb28181.media.MediaSinkImpl -- Auto-created track: type=TrackVideo, codec=CodecH264
18:19:56.083 [main] DEBUG com.zlmediakit.gb28181.media.MediaSinkImpl -- Added track: type=TrackVideo, codec=CodecH264, id=1
18:19:56.084 [main] DEBUG com.zlmediakit.gb28181.media.MediaSinkImpl -- MediaSink ready state changed: true
]]></system-out>
  </testcase>
  <testcase name="testTrackCreation" classname="com.zlmediakit.gb28181.media.MediaSinkTest" time="0.001">
    <system-out><![CDATA[18:19:56.085 [main] DEBUG com.zlmediakit.gb28181.media.MediaSinkImpl -- Created MediaSinkImpl
18:19:56.085 [main] DEBUG com.zlmediakit.gb28181.media.TrackImpl -- Created track: type=TrackVideo, codec=CodecH264
18:19:56.085 [main] DEBUG com.zlmediakit.gb28181.media.MediaSinkImpl -- Auto-created track: type=TrackVideo, codec=CodecH264
18:19:56.085 [main] DEBUG com.zlmediakit.gb28181.media.MediaSinkImpl -- Added track: type=TrackVideo, codec=CodecH264, id=1
18:19:56.085 [main] DEBUG com.zlmediakit.gb28181.media.MediaSinkImpl -- MediaSink ready state changed: true
]]></system-out>
  </testcase>
  <testcase name="testSameCodecMultipleFrames" classname="com.zlmediakit.gb28181.media.MediaSinkTest" time="0">
    <system-out><![CDATA[18:19:56.086 [main] DEBUG com.zlmediakit.gb28181.media.MediaSinkImpl -- Created MediaSinkImpl
18:19:56.086 [main] DEBUG com.zlmediakit.gb28181.media.TrackImpl -- Created track: type=TrackVideo, codec=CodecH264
18:19:56.086 [main] DEBUG com.zlmediakit.gb28181.media.MediaSinkImpl -- Auto-created track: type=TrackVideo, codec=CodecH264
18:19:56.086 [main] DEBUG com.zlmediakit.gb28181.media.MediaSinkImpl -- Added track: type=TrackVideo, codec=CodecH264, id=1
18:19:56.086 [main] DEBUG com.zlmediakit.gb28181.media.MediaSinkImpl -- MediaSink ready state changed: true
]]></system-out>
  </testcase>
  <testcase name="testTrackStatistics" classname="com.zlmediakit.gb28181.media.MediaSinkTest" time="0.002">
    <system-out><![CDATA[18:19:56.086 [main] DEBUG com.zlmediakit.gb28181.media.MediaSinkImpl -- Created MediaSinkImpl
18:19:56.086 [main] DEBUG com.zlmediakit.gb28181.media.TrackImpl -- Created track: type=TrackVideo, codec=CodecH264
18:19:56.086 [main] DEBUG com.zlmediakit.gb28181.media.MediaSinkImpl -- Auto-created track: type=TrackVideo, codec=CodecH264
18:19:56.087 [main] DEBUG com.zlmediakit.gb28181.media.MediaSinkImpl -- Added track: type=TrackVideo, codec=CodecH264, id=1
18:19:56.087 [main] DEBUG com.zlmediakit.gb28181.media.MediaSinkImpl -- MediaSink ready state changed: true
]]></system-out>
  </testcase>
  <testcase name="testNullFrame" classname="com.zlmediakit.gb28181.media.MediaSinkTest" time="0.001">
    <system-out><![CDATA[18:19:56.088 [main] DEBUG com.zlmediakit.gb28181.media.MediaSinkImpl -- Created MediaSinkImpl
]]></system-out>
  </testcase>
  <testcase name="testConcurrentAccess" classname="com.zlmediakit.gb28181.media.MediaSinkTest" time="0.035">
    <system-out><![CDATA[18:19:56.089 [main] DEBUG com.zlmediakit.gb28181.media.MediaSinkImpl -- Created MediaSinkImpl
18:19:56.091 [Thread-1] DEBUG com.zlmediakit.gb28181.media.TrackImpl -- Created track: type=TrackVideo, codec=CodecH264
18:19:56.091 [Thread-1] DEBUG com.zlmediakit.gb28181.media.MediaSinkImpl -- Auto-created track: type=TrackVideo, codec=CodecH264
18:19:56.091 [Thread-1] DEBUG com.zlmediakit.gb28181.media.MediaSinkImpl -- Added track: type=TrackVideo, codec=CodecH264, id=1
18:19:56.091 [Thread-1] DEBUG com.zlmediakit.gb28181.media.MediaSinkImpl -- MediaSink ready state changed: true
18:19:56.091 [Thread-2] DEBUG com.zlmediakit.gb28181.media.TrackImpl -- Created track: type=TrackVideo, codec=CodecH264
18:19:56.091 [Thread-2] DEBUG com.zlmediakit.gb28181.media.MediaSinkImpl -- Auto-created track: type=TrackVideo, codec=CodecH264
18:19:56.092 [Thread-2] WARN com.zlmediakit.gb28181.media.MediaSinkImpl -- Track type TrackVideo already exists
]]></system-out>
  </testcase>
</testsuite>