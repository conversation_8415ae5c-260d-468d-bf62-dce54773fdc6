package com.zlmediakit.gb28181.media;

import java.util.function.Consumer;

/**
 * 媒体接收器接口
 * 基于ZLMediaKit的MediaSink设计
 * 
 * <AUTHOR> Java Port
 */
public interface MediaSink {
    
    /**
     * 输入媒体帧
     * 
     * @param frame 媒体帧
     * @return 是否处理成功
     */
    boolean inputFrame(Frame frame);
    
    /**
     * 添加轨道
     * 
     * @param track 轨道
     * @return 是否添加成功
     */
    boolean addTrack(Track track);
    
    /**
     * 移除轨道
     * 
     * @param track 轨道
     * @return 是否移除成功
     */
    boolean removeTrack(Track track);
    
    /**
     * 获取轨道数量
     * 
     * @return 轨道数量
     */
    int getTrackCount();
    
    /**
     * 获取指定类型的轨道
     * 
     * @param trackType 轨道类型
     * @return 轨道，如果不存在返回null
     */
    Track getTrack(Frame.TrackType trackType);
    
    /**
     * 设置帧回调
     * 
     * @param callback 帧回调函数
     */
    void setOnFrame(Consumer<Frame> callback);
    
    /**
     * 刷新缓存
     */
    void flush();
    
    /**
     * 重置状态
     */
    void reset();
    
    /**
     * 获取统计信息
     * 
     * @return 统计信息
     */
    String getStatistics();
    
    /**
     * 检查是否就绪
     * 
     * @return boolean
     */
    boolean isReady();
}
