# GB28181 Java Implementation Configuration
# Based on ZLMediaKit conf/config.ini

# RTP Proxy Configuration
rtp.proxy.port=10000
rtp.proxy.timeout.sec=15
rtp.proxy.dump.dir=
rtp.proxy.h264.pt=98
rtp.proxy.h265.pt=99
rtp.proxy.ps.pt=96
rtp.proxy.opus.pt=97
rtp.proxy.merge.frame=true

# Network Configuration
network.tcp.mode=PASSIVE
network.reuse.port=true
network.multiplex=false
network.only.track=ALL

# Session Configuration
session.timeout.sec=30
session.check.interval.sec=3
session.max.cached.frame.ms=3000

# Media Configuration
media.vhost=__defaultVhost__
media.app=rtp
media.schema=rtp

# Logging Configuration
logging.level=INFO
logging.dump.rtp=false
logging.dump.video=false

# Thread Pool Configuration
thread.pool.core.size=4
thread.pool.max.size=16
thread.pool.queue.capacity=1000

# Buffer Configuration
buffer.rtp.size=32768
buffer.frame.cache.size=100

# Decoder Configuration
decoder.ts.packet.size=188
decoder.ps.buffer.size=65536
decoder.auto.detect=true

# Statistics Configuration
stats.enable=true
stats.report.interval.sec=60
