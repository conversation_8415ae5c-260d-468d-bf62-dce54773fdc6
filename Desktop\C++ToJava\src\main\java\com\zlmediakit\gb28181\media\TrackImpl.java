package com.zlmediakit.gb28181.media;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.atomic.AtomicLong;

/**
 * 媒体轨道实现类
 * 基于ZLMediaKit的Track实现
 * 
 * <AUTHOR> Java Port
 */
public class TrackImpl implements Track {
    private static final Logger logger = LoggerFactory.getLogger(TrackImpl.class);
    
    // 轨道信息
    private final Frame.TrackType trackType;
    private final Frame.CodecId codecId;
    private int trackId;
    private int bitRate;
    private double frameRate;
    
    // 配置帧
    private Frame configFrame;
    
    // 状态
    private boolean ready = false;
    
    // 统计信息
    private final AtomicLong totalFrames = new AtomicLong(0);
    private final AtomicLong totalBytes = new AtomicLong(0);
    private final AtomicLong keyFrames = new AtomicLong(0);
    private long lastFrameTime = 0;
    private long firstFrameTime = 0;
    
    /**
     * 构造函数
     * 
     * @param trackType 轨道类型
     * @param codecId 编解码器ID
     */
    public TrackImpl(Frame.TrackType trackType, Frame.CodecId codecId) {
        this.trackType = trackType;
        this.codecId = codecId;
        this.trackId = trackType.ordinal();
        
        logger.debug("Created track: type={}, codec={}", trackType, codecId);
    }
    
    /**
     * 构造函数
     * 
     * @param trackType 轨道类型
     * @param codecId 编解码器ID
     * @param trackId 轨道ID
     */
    public TrackImpl(Frame.TrackType trackType, Frame.CodecId codecId, int trackId) {
        this.trackType = trackType;
        this.codecId = codecId;
        this.trackId = trackId;
        
        logger.debug("Created track: type={}, codec={}, id={}", trackType, codecId, trackId);
    }
    
    @Override
    public Frame.TrackType getTrackType() {
        return trackType;
    }
    
    @Override
    public Frame.CodecId getCodecId() {
        return codecId;
    }
    
    @Override
    public String getCodecName() {
        return Frame.getCodecName(codecId);
    }
    
    @Override
    public int getTrackId() {
        return trackId;
    }
    
    @Override
    public void setTrackId(int trackId) {
        this.trackId = trackId;
    }
    
    @Override
    public int getBitRate() {
        return bitRate;
    }
    
    @Override
    public void setBitRate(int bitRate) {
        this.bitRate = bitRate;
    }
    
    @Override
    public double getFrameRate() {
        return frameRate;
    }
    
    @Override
    public void setFrameRate(double frameRate) {
        this.frameRate = frameRate;
    }
    
    @Override
    public Frame getConfigFrame() {
        return configFrame;
    }
    
    @Override
    public void setConfigFrame(Frame configFrame) {
        this.configFrame = configFrame;
        if (configFrame != null) {
            this.ready = true;
            logger.debug("Set config frame for track {}: size={}", trackId, configFrame.getSize());
        }
    }
    
    @Override
    public boolean isReady() {
        return ready;
    }
    
    @Override
    public boolean inputFrame(Frame frame) {
        if (frame == null) {
            return false;
        }
        
        try {
            // 检查帧类型匹配
            if (frame.getTrackType() != trackType || frame.getCodecId() != codecId) {
                logger.warn("Frame type mismatch: expected {}/{}, got {}/{}", 
                           trackType, codecId, frame.getTrackType(), frame.getCodecId());
                return false;
            }
            
            // 更新统计信息
            updateStatistics(frame);
            
            // 如果是配置帧，保存它
            if (frame.isConfigFrame() && configFrame == null) {
                setConfigFrame(new FrameImpl(frame));
            }
            
            // 标记为就绪
            if (!ready) {
                ready = true;
            }
            
            return true;
            
        } catch (Exception e) {
            logger.error("Error processing frame in track {}", trackId, e);
            return false;
        }
    }
    
    /**
     * 更新统计信息
     * 
     * @param frame 帧
     */
    private void updateStatistics(Frame frame) {
        long currentTime = System.currentTimeMillis();
        
        // 更新计数器
        totalFrames.incrementAndGet();
        totalBytes.addAndGet(frame.getSize());
        
        if (frame.isKeyFrame()) {
            keyFrames.incrementAndGet();
        }
        
        // 更新时间
        if (firstFrameTime == 0) {
            firstFrameTime = currentTime;
        }
        lastFrameTime = currentTime;
        
        // 计算帧率
        if (totalFrames.get() > 1 && firstFrameTime > 0) {
            long duration = lastFrameTime - firstFrameTime;
            if (duration > 0) {
                frameRate = (totalFrames.get() - 1) * 1000.0 / duration;
            }
        }
        
        // 计算比特率
        if (firstFrameTime > 0) {
            long duration = lastFrameTime - firstFrameTime;
            if (duration > 1000) { // 至少1秒的数据
                bitRate = (int) (totalBytes.get() * 8 * 1000 / duration);
            }
        }
    }
    
    @Override
    public String getStatistics() {
        StringBuilder sb = new StringBuilder();
        sb.append("Track Statistics (ID: ").append(trackId).append("):\n");
        sb.append("  Type: ").append(trackType).append("\n");
        sb.append("  Codec: ").append(getCodecName()).append("\n");
        sb.append("  Total Frames: ").append(totalFrames.get()).append("\n");
        sb.append("  Key Frames: ").append(keyFrames.get()).append("\n");
        sb.append("  Total Bytes: ").append(totalBytes.get()).append("\n");
        sb.append("  Frame Rate: ").append(String.format("%.2f", frameRate)).append(" fps\n");
        sb.append("  Bit Rate: ").append(bitRate).append(" bps\n");
        sb.append("  Ready: ").append(ready).append("\n");
        sb.append("  Has Config: ").append(configFrame != null).append("\n");
        
        if (firstFrameTime > 0) {
            long duration = (lastFrameTime - firstFrameTime) / 1000;
            sb.append("  Duration: ").append(duration).append(" seconds\n");
        }
        
        return sb.toString();
    }
    
    @Override
    public void resetStatistics() {
        totalFrames.set(0);
        totalBytes.set(0);
        keyFrames.set(0);
        firstFrameTime = 0;
        lastFrameTime = 0;
        frameRate = 0;
        bitRate = 0;
        
        logger.debug("Reset statistics for track {}", trackId);
    }
    
    @Override
    public String toString() {
        return "TrackImpl{" +
               "trackType=" + trackType +
               ", codecId=" + codecId +
               ", trackId=" + trackId +
               ", bitRate=" + bitRate +
               ", frameRate=" + frameRate +
               ", ready=" + ready +
               '}';
    }
}
