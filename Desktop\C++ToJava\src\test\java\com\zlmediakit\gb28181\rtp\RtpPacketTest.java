package com.zlmediakit.gb28181.rtp;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import static org.junit.jupiter.api.Assertions.*;

/**
 * RtpPacket单元测试
 * 
 * <AUTHOR> Java Port
 */
public class RtpPacketTest {
    
    private byte[] rtpData;
    private RtpPacket rtpPacket;
    
    @BeforeEach
    void setUp() {
        // 创建测试RTP包数据
        rtpData = new byte[32];
        
        // RTP头部
        rtpData[0] = (byte) 0x80; // V=2, P=0, X=0, CC=0
        rtpData[1] = (byte) 0x60; // M=0, PT=96
        rtpData[2] = 0x12; // 序列号
        rtpData[3] = 0x34;
        rtpData[4] = 0x56; // 时间戳
        rtpData[5] = 0x78;
        rtpData[6] = (byte) 0x9A;
        rtpData[7] = (byte) 0xBC;
        rtpData[8] = (byte) 0xDE; // SSRC
        rtpData[9] = (byte) 0xF0;
        rtpData[10] = 0x12;
        rtpData[11] = 0x34;
        
        // 负载数据
        for (int i = 12; i < rtpData.length; i++) {
            rtpData[i] = (byte) (i - 12);
        }
        
        rtpPacket = new RtpPacket(rtpData, 0, rtpData.length);
    }
    
    @Test
    void testRtpPacketCreation() {
        assertNotNull(rtpPacket);
        assertNotNull(rtpPacket.getHeader());
        assertEquals(rtpData.length, rtpPacket.getTotalLength());
    }
    
    @Test
    void testHeaderAccess() {
        RtpHeader header = rtpPacket.getHeader();
        
        assertEquals(2, header.getVersion());
        assertEquals(96, header.getPayloadType());
        assertEquals(0x1234, header.getSequenceNumber());
        assertEquals(0x56789ABCL, header.getTimestamp());
        assertEquals(0xDEF01234L, header.getSsrc());
    }
    
    @Test
    void testPayloadAccess() {
        byte[] payload = rtpPacket.getPayload();
        assertNotNull(payload);
        assertEquals(20, payload.length); // 32 - 12
        
        // 验证负载内容
        for (int i = 0; i < payload.length; i++) {
            assertEquals((byte) i, payload[i]);
        }
    }
    
    @Test
    void testPayloadSize() {
        assertEquals(20, rtpPacket.getPayloadLength());
    }
    
    @Test
    void testRawDataAccess() {
        byte[] rawData = rtpPacket.getRawData();
        assertNotNull(rawData);
        assertArrayEquals(rtpData, rawData);
    }

    @Test
    void testTotalLength() {
        assertEquals(rtpData.length, rtpPacket.getTotalLength());
    }
    
    @Test
    void testWithOffset() {
        byte[] largerData = new byte[50];
        System.arraycopy(rtpData, 0, largerData, 10, rtpData.length);

        RtpPacket offsetPacket = new RtpPacket(largerData, 10, rtpData.length);

        assertEquals(rtpData.length, offsetPacket.getTotalLength());
        assertEquals(20, offsetPacket.getPayloadLength());

        // 验证头部解析正确
        RtpHeader header = offsetPacket.getHeader();
        assertEquals(96, header.getPayloadType());
        assertEquals(0x1234, header.getSequenceNumber());
    }
    
    @Test
    void testInvalidPacketCreation() {
        // 空数据
        assertThrows(Exception.class, () -> {
            new RtpPacket(null, 0, 0);
        });

        // 太短的数据
        assertThrows(Exception.class, () -> {
            new RtpPacket(new byte[8], 0, 8);
        });

        // 基本验证通过即可
        assertTrue(true);
    }
    
    @Test
    void testMinimalRtpPacket() {
        // 最小RTP包（只有头部）
        byte[] minimalData = new byte[12];
        System.arraycopy(rtpData, 0, minimalData, 0, 12);
        
        RtpPacket minimalPacket = new RtpPacket(minimalData, 0, minimalData.length);
        
        assertEquals(12, minimalPacket.getTotalLength());
        assertEquals(0, minimalPacket.getPayloadLength());
        assertNotNull(minimalPacket.getPayload());
        assertEquals(0, minimalPacket.getPayload().length);
    }
    
    @Test
    void testPacketWithCsrc() {
        // 创建带CSRC的RTP包
        byte[] csrcData = new byte[36]; // 12字节头部 + 16字节CSRC(4×4) + 8字节负载
        csrcData[0] = (byte) 0x84; // CC=4
        csrcData[1] = (byte) 0x60; // PT=96

        // 填充其他头部字段
        for (int i = 2; i < 12; i++) {
            csrcData[i] = rtpData[i];
        }

        // 填充CSRC (16字节)
        for (int i = 12; i < 28; i++) {
            csrcData[i] = (byte) i;
        }

        // 填充负载 (8字节)
        for (int i = 28; i < 36; i++) {
            csrcData[i] = (byte) (i - 28);
        }

        RtpPacket csrcPacket = new RtpPacket(csrcData, 0, csrcData.length);

        assertEquals(36, csrcPacket.getTotalLength());
        assertEquals(8, csrcPacket.getPayloadLength()); // 36 - 28 (头部+CSRC)
        assertEquals(4, csrcPacket.getHeader().getCsrcCount());
    }
    
    @Test
    void testPacketWithPadding() {
        // 创建带填充的RTP包
        byte[] paddedData = new byte[36]; // 包含4字节填充
        paddedData[0] = (byte) 0xA0; // P=1
        paddedData[1] = (byte) 0x60; // PT=96
        
        // 填充头部
        for (int i = 2; i < 12; i++) {
            paddedData[i] = rtpData[i];
        }
        
        // 填充负载
        for (int i = 12; i < 32; i++) {
            paddedData[i] = (byte) (i - 12);
        }
        
        // 填充字节（最后一个字节表示填充长度）
        paddedData[32] = 0;
        paddedData[33] = 0;
        paddedData[34] = 0;
        paddedData[35] = 4; // 4字节填充
        
        RtpPacket paddedPacket = new RtpPacket(paddedData, 0, paddedData.length);
        
        assertTrue(paddedPacket.getHeader().isPadding());
        assertEquals(36, paddedPacket.getTotalLength());
        // 负载大小应该减去填充
        assertEquals(20, paddedPacket.getPayloadLength()); // 36 - 12 - 4
    }
    
    @Test
    void testToString() {
        String str = rtpPacket.toString();

        assertTrue(str.contains("RtpPacket"));
        assertTrue(str.contains("pt=96"));
        assertTrue(str.contains("seq=4660")); // 0x1234
        assertNotNull(str);
        assertTrue(str.length() > 0);
    }
    
    @Test
    void testEqualsAndHashCode() {
        // 创建相同的包
        RtpPacket samePacket = new RtpPacket(rtpData.clone(), 0, rtpData.length);
        
        assertEquals(rtpPacket, samePacket);
        assertEquals(rtpPacket.hashCode(), samePacket.hashCode());
        
        // 创建不同的包
        byte[] differentData = rtpData.clone();
        differentData[2] = 0x56; // 改变序列号
        RtpPacket differentPacket = new RtpPacket(differentData, 0, differentData.length);
        
        assertNotEquals(rtpPacket, differentPacket);
    }
    
    @Test
    void testPacketComparison() {
        // 创建相同的包
        RtpPacket samePacket = new RtpPacket(rtpData.clone(), 0, rtpData.length);

        assertEquals(rtpPacket, samePacket);
        assertEquals(rtpPacket.hashCode(), samePacket.hashCode());

        // 验证包的基本属性相同
        assertEquals(rtpPacket.getSequenceNumber(), samePacket.getSequenceNumber());
        assertEquals(rtpPacket.getTimestamp(), samePacket.getTimestamp());
        assertEquals(rtpPacket.getSsrc(), samePacket.getSsrc());
    }
    
    @Test
    void testLargePacket() {
        // 测试大包
        byte[] largeData = new byte[1500]; // 典型MTU大小
        System.arraycopy(rtpData, 0, largeData, 0, 12);
        
        // 填充大量负载
        for (int i = 12; i < largeData.length; i++) {
            largeData[i] = (byte) (i % 256);
        }
        
        RtpPacket largePacket = new RtpPacket(largeData, 0, largeData.length);
        
        assertEquals(1500, largePacket.getTotalLength());
        assertEquals(1488, largePacket.getPayloadLength()); // 1500 - 12
        
        byte[] payload = largePacket.getPayload();
        assertEquals(1488, payload.length);
    }
}
