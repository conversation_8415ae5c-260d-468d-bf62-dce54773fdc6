package com.zlmediakit.gb28181.protocol;

import com.zlmediakit.gb28181.decoder.container.PSDecoder;
import com.zlmediakit.gb28181.decoder.container.TSDecoder;
import com.zlmediakit.gb28181.decoder.rtp.CommonRtpDecoder;
import com.zlmediakit.gb28181.decoder.rtp.H264RtpDecoder;
import com.zlmediakit.gb28181.decoder.rtp.H265RtpDecoder;
import com.zlmediakit.gb28181.decoder.rtp.RtpDecoder;
import com.zlmediakit.gb28181.media.Frame;
import com.zlmediakit.gb28181.media.MediaTuple;
import com.zlmediakit.gb28181.rtp.RtpPacket;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Consumer;

/**
 * GB28181协议处理器
 * 基于ZLMediaKit的GB28181Process实现
 * 负责RTP包的协议解析和分发
 * 
 * <AUTHOR> Java Port
 */
public class GB28181Process {
    private static final Logger logger = LoggerFactory.getLogger(GB28181Process.class);
    
    // 默认负载类型配置
    private static final int DEFAULT_H264_PT = 98;
    private static final int DEFAULT_H265_PT = 99;
    private static final int DEFAULT_PS_PT = 96;
    private static final int DEFAULT_OPUS_PT = 97;
    
    // 媒体信息
    private final MediaTuple mediaTuple;
    private final Consumer<Frame> frameCallback;
    
    // RTP解码器映射
    private final ConcurrentHashMap<Integer, RtpDecoder> rtpDecoders = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<Integer, RtpReceiver> rtpReceivers = new ConcurrentHashMap<>();
    
    // 容器解码器
    private TSDecoder tsDecoder;
    private PSDecoder psDecoder;
    
    // 配置参数
    private int h264Pt = DEFAULT_H264_PT;
    private int h265Pt = DEFAULT_H265_PT;
    private int psPt = DEFAULT_PS_PT;
    private int opusPt = DEFAULT_OPUS_PT;
    
    /**
     * 构造函数
     * 
     * @param mediaTuple 媒体元组
     * @param frameCallback 帧回调
     */
    public GB28181Process(MediaTuple mediaTuple, Consumer<Frame> frameCallback) {
        this.mediaTuple = mediaTuple;
        this.frameCallback = frameCallback;
        
        logger.debug("Created GB28181Process for stream: {}", mediaTuple.getStream());
    }
    
    /**
     * 输入RTP数据
     * 
     * @param isUdp 是否UDP模式
     * @param data RTP数据
     * @param offset 偏移量
     * @param length 长度
     * @return 是否处理成功
     */
    public boolean inputRtp(boolean isUdp, byte[] data, int offset, int length) {
        try {
            // 创建RTP包
            RtpPacket rtpPacket = RtpPacket.create(data, offset, length);
            if (rtpPacket == null) {
                logger.debug("Invalid RTP packet");
                return false;
            }
            
            // 获取负载类型
            int pt = rtpPacket.getPayloadType();
            
            // 获取或创建RTP接收器
            RtpReceiver receiver = getOrCreateRtpReceiver(pt);
            if (receiver == null) {
                logger.warn("No RTP receiver for payload type: {}", pt);
                return false;
            }
            
            // 处理RTP包
            receiver.inputRtp(rtpPacket);
            
            return true;
            
        } catch (Exception e) {
            logger.error("Error processing RTP packet", e);
            return false;
        }
    }
    
    /**
     * 获取或创建RTP接收器
     * 
     * @param pt 负载类型
     * @return RTP接收器
     */
    private RtpReceiver getOrCreateRtpReceiver(int pt) {
        return rtpReceivers.computeIfAbsent(pt, this::createRtpReceiver);
    }
    
    /**
     * 创建RTP接收器
     * 
     * @param pt 负载类型
     * @return RTP接收器
     */
    private RtpReceiver createRtpReceiver(int pt) {
        try {
            // 创建RTP接收器
            RtpReceiver receiver = new RtpReceiver(90000, this::onRtpSorted);
            
            // 创建对应的RTP解码器
            RtpDecoder decoder = createRtpDecoder(pt);
            if (decoder != null) {
                rtpDecoders.put(pt, decoder);
                
                // 设置解码器回调
                decoder.setOnFrame(frame -> {
                    frame.setIndex(pt);
                    onRtpDecode(frame);
                });
            }
            
            logger.debug("Created RTP receiver for PT: {}", pt);
            return receiver;
            
        } catch (Exception e) {
            logger.error("Failed to create RTP receiver for PT: {}", pt, e);
            return null;
        }
    }
    
    /**
     * 创建RTP解码器
     * 
     * @param pt 负载类型
     * @return RTP解码器
     */
    private RtpDecoder createRtpDecoder(int pt) {
        // 检查标准负载类型
        if (pt < 96) {
            Frame.CodecId codecId = getStandardCodec(pt);
            if (codecId != Frame.CodecId.CodecInvalid) {
                return createDecoderByCodec(codecId);
            }
        }
        
        // 检查动态负载类型
        if (pt == h264Pt) {
            logger.debug("Creating H264 RTP decoder for PT: {}", pt);
            return new H264RtpDecoder();
        }
        
        if (pt == h265Pt) {
            logger.debug("Creating H265 RTP decoder for PT: {}", pt);
            return new H265RtpDecoder();
        }
        
        // 默认作为TS/PS处理
        logger.debug("Creating common RTP decoder for PT: {} (TS/PS)", pt);
        return new CommonRtpDecoder(32 * 1024);
    }
    
    /**
     * 根据编解码器创建解码器
     * 
     * @param codecId 编解码器ID
     * @return RTP解码器
     */
    private RtpDecoder createDecoderByCodec(Frame.CodecId codecId) {
        switch (codecId) {
            case CodecH264:
                return new H264RtpDecoder();
            case CodecH265:
                return new H265RtpDecoder();
            default:
                return new CommonRtpDecoder(32 * 1024);
        }
    }
    
    /**
     * 获取标准负载类型对应的编解码器
     * 
     * @param pt 负载类型
     * @return 编解码器ID
     */
    private Frame.CodecId getStandardCodec(int pt) {
        // 这里可以根据RFC标准映射
        // 目前简化处理
        return Frame.CodecId.CodecInvalid;
    }
    
    /**
     * RTP排序回调
     * 
     * @param rtpPacket RTP包
     */
    private void onRtpSorted(RtpPacket rtpPacket) {
        try {
            int pt = rtpPacket.getPayloadType();
            RtpDecoder decoder = rtpDecoders.get(pt);
            
            if (decoder != null) {
                decoder.inputRtp(rtpPacket);
            } else {
                logger.debug("No decoder for PT: {}", pt);
            }
            
        } catch (Exception e) {
            logger.error("Error in RTP sorted callback", e);
        }
    }
    
    /**
     * RTP解码回调
     * 
     * @param frame 解码后的帧
     */
    private void onRtpDecode(Frame frame) {
        try {
            Frame.CodecId codecId = frame.getCodecId();
            
            // 检查是否为容器格式
            if (codecId == Frame.CodecId.CodecInvalid || 
                codecId == Frame.CodecId.CodecTS || 
                codecId == Frame.CodecId.CodecPS) {
                
                // 处理TS/PS容器
                processContainerFrame(frame);
            } else {
                // 直接输出媒体帧
                if (frameCallback != null) {
                    frameCallback.accept(frame);
                }
            }
            
        } catch (Exception e) {
            logger.error("Error in RTP decode callback", e);
        }
    }
    
    /**
     * 处理容器格式帧
     * 
     * @param frame 容器帧
     */
    private void processContainerFrame(Frame frame) {
        byte[] data = frame.getData();
        if (data == null || data.length == 0) {
            return;
        }
        
        // 自动检测TS/PS格式
        if (isTS(data)) {
            // 处理TS流
            if (tsDecoder == null) {
                tsDecoder = new TSDecoder();
                tsDecoder.setOnFrame(frameCallback);
                logger.debug("Created TS decoder for stream: {}", mediaTuple.getStream());
            }
            tsDecoder.input(data);
            
        } else {
            // 处理PS流
            if (psDecoder == null) {
                psDecoder = new PSDecoder();
                psDecoder.setOnFrame(frameCallback);
                logger.debug("Created PS decoder for stream: {}", mediaTuple.getStream());
            }
            psDecoder.input(data);
        }
    }
    
    /**
     * 检查是否为TS格式
     * 
     * @param data 数据
     * @return boolean
     */
    private boolean isTS(byte[] data) {
        // TS包固定188字节，同步字节0x47
        return data.length % 188 == 0 && data[0] == 0x47;
    }
    
    /**
     * 刷新所有缓存
     */
    public void flush() {
        try {
            // 刷新RTP解码器
            rtpDecoders.values().forEach(decoder -> {
                try {
                    decoder.flush();
                } catch (Exception e) {
                    logger.error("Error flushing RTP decoder", e);
                }
            });
            
            // 刷新容器解码器
            if (tsDecoder != null) {
                tsDecoder.flush();
            }
            if (psDecoder != null) {
                psDecoder.flush();
            }
            
        } catch (Exception e) {
            logger.error("Error flushing GB28181Process", e);
        }
    }
    
    // Getter和Setter方法
    
    public int getH264Pt() {
        return h264Pt;
    }
    
    public void setH264Pt(int h264Pt) {
        this.h264Pt = h264Pt;
    }
    
    public int getH265Pt() {
        return h265Pt;
    }
    
    public void setH265Pt(int h265Pt) {
        this.h265Pt = h265Pt;
    }
    
    public int getPsPt() {
        return psPt;
    }
    
    public void setPsPt(int psPt) {
        this.psPt = psPt;
    }
    
    public int getOpusPt() {
        return opusPt;
    }
    
    public void setOpusPt(int opusPt) {
        this.opusPt = opusPt;
    }
}
