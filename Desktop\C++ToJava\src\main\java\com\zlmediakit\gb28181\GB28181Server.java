package com.zlmediakit.gb28181;

import com.zlmediakit.gb28181.media.Frame;
import com.zlmediakit.gb28181.network.RtpServer;
import com.zlmediakit.gb28181.session.SessionManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.CompletableFuture;
import java.util.function.Consumer;

/**
 * GB28181服务器主类
 * 基于ZLMediaKit的RtpServer实现
 * 
 * <AUTHOR> Java Port
 */
public class GB28181Server {
    private static final Logger logger = LoggerFactory.getLogger(GB28181Server.class);
    
    private RtpServer rtpServer;
    private SessionManager sessionManager;
    private boolean running = false;
    
    // 配置参数
    private int port = 10000;
    private String localIp = "0.0.0.0";
    private int timeoutSec = 15;
    private TcpMode tcpMode = TcpMode.PASSIVE;
    private boolean reusePort = true;
    private boolean multiplex = false;
    
    // 回调函数
    private Consumer<Frame> onFrameCallback;
    private Runnable onStartCallback;
    private Runnable onStopCallback;
    
    /**
     * TCP模式枚举
     */
    public enum TcpMode {
        NONE,     // 仅UDP
        PASSIVE,  // TCP被动模式
        ACTIVE    // TCP主动模式
    }
    
    /**
     * 构造函数
     */
    public GB28181Server() {
        this.sessionManager = new SessionManager();
    }
    
    /**
     * 启动服务器
     * 
     * @return CompletableFuture<Void>
     */
    public CompletableFuture<Void> start() {
        return CompletableFuture.runAsync(() -> {
            try {
                if (running) {
                    logger.warn("Server is already running");
                    return;
                }
                
                logger.info("Starting GB28181 Server on {}:{}", localIp, port);
                
                // 创建RTP服务器
                rtpServer = new RtpServer();
                
                // 配置服务器参数
                rtpServer.setPort(port);
                rtpServer.setLocalIp(localIp);
                rtpServer.setTcpMode(tcpMode);
                rtpServer.setReusePort(reusePort);
                rtpServer.setMultiplex(multiplex);
                rtpServer.setTimeoutSec(timeoutSec);
                
                // 设置回调
                if (onFrameCallback != null) {
                    rtpServer.setOnFrame(onFrameCallback);
                }
                
                // 启动服务器
                rtpServer.start();
                
                running = true;
                logger.info("GB28181 Server started successfully");
                
                if (onStartCallback != null) {
                    onStartCallback.run();
                }
                
            } catch (Exception e) {
                logger.error("Failed to start GB28181 Server", e);
                throw new RuntimeException("Failed to start server", e);
            }
        });
    }
    
    /**
     * 停止服务器
     * 
     * @return CompletableFuture<Void>
     */
    public CompletableFuture<Void> stop() {
        return CompletableFuture.runAsync(() -> {
            try {
                if (!running) {
                    logger.warn("Server is not running");
                    return;
                }
                
                logger.info("Stopping GB28181 Server");
                
                if (rtpServer != null) {
                    rtpServer.stop();
                }
                
                if (sessionManager != null) {
                    sessionManager.shutdown();
                }
                
                running = false;
                logger.info("GB28181 Server stopped successfully");
                
                if (onStopCallback != null) {
                    onStopCallback.run();
                }
                
            } catch (Exception e) {
                logger.error("Failed to stop GB28181 Server", e);
                throw new RuntimeException("Failed to stop server", e);
            }
        });
    }
    
    /**
     * 检查服务器是否运行中
     * 
     * @return boolean
     */
    public boolean isRunning() {
        return running;
    }
    
    // Getter和Setter方法
    
    public int getPort() {
        return port;
    }
    
    public void setPort(int port) {
        this.port = port;
    }
    
    public String getLocalIp() {
        return localIp;
    }
    
    public void setLocalIp(String localIp) {
        this.localIp = localIp;
    }
    
    public int getTimeoutSec() {
        return timeoutSec;
    }
    
    public void setTimeoutSec(int timeoutSec) {
        this.timeoutSec = timeoutSec;
    }
    
    public TcpMode getTcpMode() {
        return tcpMode;
    }
    
    public void setTcpMode(TcpMode tcpMode) {
        this.tcpMode = tcpMode;
    }
    
    public boolean isReusePort() {
        return reusePort;
    }
    
    public void setReusePort(boolean reusePort) {
        this.reusePort = reusePort;
    }
    
    public boolean isMultiplex() {
        return multiplex;
    }
    
    public void setMultiplex(boolean multiplex) {
        this.multiplex = multiplex;
    }
    
    public void setOnFrame(Consumer<Frame> callback) {
        this.onFrameCallback = callback;
    }
    
    public void setOnStart(Runnable callback) {
        this.onStartCallback = callback;
    }
    
    public void setOnStop(Runnable callback) {
        this.onStopCallback = callback;
    }
    
    /**
     * 主函数 - 用于测试
     */
    public static void main(String[] args) {
        GB28181Server server = new GB28181Server();
        
        // 设置回调
        server.setOnFrame(frame -> {
            System.out.println("Received frame: " + frame.getCodecName() + 
                             ", size: " + frame.getSize() + 
                             ", dts: " + frame.getDts());
        });
        
        server.setOnStart(() -> {
            System.out.println("Server started!");
        });
        
        server.setOnStop(() -> {
            System.out.println("Server stopped!");
        });
        
        // 启动服务器
        server.start().join();
        
        // 添加关闭钩子
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            server.stop().join();
        }));
        
        // 保持运行
        try {
            Thread.currentThread().join();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }
}
