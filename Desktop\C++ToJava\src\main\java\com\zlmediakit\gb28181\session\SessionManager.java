package com.zlmediakit.gb28181.session;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 会话管理器
 * 基于ZLMediaKit的会话管理逻辑
 * 负责会话的生命周期管理和超时检查
 * 
 * <AUTHOR> Java Port
 */
public class SessionManager {
    private static final Logger logger = LoggerFactory.getLogger(SessionManager.class);
    
    private final ConcurrentHashMap<String, RtpSession> sessions = new ConcurrentHashMap<>();
    private final AtomicBoolean running = new AtomicBoolean(false);
    
    // 定时器相关
    private ScheduledExecutorService scheduler;
    private ScheduledFuture<?> timeoutCheckTask;
    
    // 配置参数
    private int checkIntervalSec = 3;  // 检查间隔（秒）
    
    /**
     * 构造函数
     */
    public SessionManager() {
    }
    
    /**
     * 启动会话管理器
     */
    public void start() {
        if (running.get()) {
            logger.warn("SessionManager is already running");
            return;
        }
        
        logger.info("Starting SessionManager");
        
        // 创建定时器
        scheduler = Executors.newSingleThreadScheduledExecutor(r -> {
            Thread t = new Thread(r, "SessionManager-Timer");
            t.setDaemon(true);
            return t;
        });
        
        // 启动超时检查任务
        timeoutCheckTask = scheduler.scheduleWithFixedDelay(
            this::checkTimeouts,
            checkIntervalSec,
            checkIntervalSec,
            TimeUnit.SECONDS
        );
        
        running.set(true);
        logger.info("SessionManager started");
    }
    
    /**
     * 停止会话管理器
     */
    public void shutdown() {
        if (!running.get()) {
            return;
        }
        
        logger.info("Shutting down SessionManager");
        
        running.set(false);
        
        // 停止定时任务
        if (timeoutCheckTask != null) {
            timeoutCheckTask.cancel(false);
        }
        
        // 关闭定时器
        if (scheduler != null) {
            scheduler.shutdown();
            try {
                if (!scheduler.awaitTermination(5, TimeUnit.SECONDS)) {
                    scheduler.shutdownNow();
                }
            } catch (InterruptedException e) {
                scheduler.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
        
        // 关闭所有会话
        sessions.values().forEach(RtpSession::close);
        sessions.clear();
        
        logger.info("SessionManager shutdown completed");
    }
    
    /**
     * 添加会话
     * 
     * @param session RTP会话
     */
    public void addSession(RtpSession session) {
        if (session == null) {
            return;
        }
        
        String sessionId = session.getSessionId();
        RtpSession existing = sessions.put(sessionId, session);
        
        if (existing != null) {
            logger.debug("Replaced existing session: {}", sessionId);
            existing.close();
        } else {
            logger.debug("Added new session: {}", sessionId);
        }
        
        // 设置会话分离回调
        session.setOnDetach((ex) -> {
            removeSession(sessionId);
        });
    }
    
    /**
     * 移除会话
     * 
     * @param sessionId 会话ID
     */
    public void removeSession(String sessionId) {
        RtpSession session = sessions.remove(sessionId);
        if (session != null) {
            logger.debug("Removed session: {}", sessionId);
        }
    }
    
    /**
     * 获取会话
     * 
     * @param sessionId 会话ID
     * @return RTP会话
     */
    public RtpSession getSession(String sessionId) {
        return sessions.get(sessionId);
    }
    
    /**
     * 获取会话数量
     * 
     * @return 会话数量
     */
    public int getSessionCount() {
        return sessions.size();
    }
    
    /**
     * 检查会话超时
     */
    private void checkTimeouts() {
        if (!running.get()) {
            return;
        }
        
        try {
            long currentTime = System.currentTimeMillis();
            
            sessions.values().removeIf(session -> {
                if (!session.isAlive(currentTime)) {
                    logger.debug("Session {} timed out", session.getSessionId());
                    session.onTimeout();
                    return true;
                }
                return false;
            });
            
        } catch (Exception e) {
            logger.error("Error checking session timeouts", e);
        }
    }
    
    /**
     * 获取会话统计信息
     * 
     * @return 统计信息字符串
     */
    public String getStatistics() {
        StringBuilder sb = new StringBuilder();
        sb.append("SessionManager Statistics:\n");
        sb.append("  Total Sessions: ").append(sessions.size()).append("\n");
        sb.append("  Running: ").append(running.get()).append("\n");
        sb.append("  Check Interval: ").append(checkIntervalSec).append("s\n");
        
        if (!sessions.isEmpty()) {
            sb.append("  Sessions:\n");
            sessions.forEach((id, session) -> {
                sb.append("    ").append(id)
                  .append(" - ").append(session.getRemoteAddress())
                  .append(" (").append(session.isUdp() ? "UDP" : "TCP").append(")")
                  .append(" - ").append(session.getTotalBytes()).append(" bytes")
                  .append("\n");
            });
        }
        
        return sb.toString();
    }
    
    /**
     * 检查管理器是否运行中
     * 
     * @return boolean
     */
    public boolean isRunning() {
        return running.get();
    }
    
    // Getter和Setter方法
    
    public int getCheckIntervalSec() {
        return checkIntervalSec;
    }
    
    public void setCheckIntervalSec(int checkIntervalSec) {
        this.checkIntervalSec = checkIntervalSec;
    }
}
