package com.zlmediakit.gb28181.decoder.container;

import com.zlmediakit.gb28181.media.Frame;
import com.zlmediakit.gb28181.media.FrameImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.nio.ByteBuffer;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Consumer;

/**
 * PS流解码器
 * 基于ZLMediaKit的PSDecoder实现
 * 支持MPEG-PS容器格式解析
 * 
 * <AUTHOR> Java Port
 */
public class PSDecoder implements ContainerDecoder {
    private static final Logger logger = LoggerFactory.getLogger(PSDecoder.class);
    
    // PS包头常量
    private static final int PS_PACK_START_CODE = 0x000001BA;
    private static final int PS_SYSTEM_HEADER_START_CODE = 0x000001BB;
    private static final int PS_PROGRAM_STREAM_MAP = 0x000001BC;
    private static final int PS_PROGRAM_END_CODE = 0x000001B9;
    
    // PES包头常量
    private static final int PES_START_CODE_PREFIX = 0x000001;
    
    // 流ID范围
    private static final int AUDIO_STREAM_ID_MIN = 0xC0;
    private static final int AUDIO_STREAM_ID_MAX = 0xDF;
    private static final int VIDEO_STREAM_ID_MIN = 0xE0;
    private static final int VIDEO_STREAM_ID_MAX = 0xEF;
    
    // 缓冲区
    private final ByteBuffer inputBuffer;
    private final ConcurrentHashMap<Integer, PSStream> streams = new ConcurrentHashMap<>();
    
    // 回调函数
    private Consumer<Frame> onFrameCallback;
    
    // 状态变量
    private boolean ready = true;
    
    // 统计信息
    private final AtomicLong totalPackets = new AtomicLong(0);
    private final AtomicLong totalBytes = new AtomicLong(0);
    private final AtomicLong outputFrames = new AtomicLong(0);
    private final AtomicLong errorPackets = new AtomicLong(0);
    
    /**
     * PS流信息
     */
    private static class PSStream {
        int streamId;
        Frame.CodecId codecId;
        Frame.TrackType trackType;
        ByteBuffer pesBuffer;
        long lastPts = -1;
        long lastDts = -1;
        
        PSStream(int streamId) {
            this.streamId = streamId;
            this.trackType = getTrackTypeFromStreamId(streamId);
            this.codecId = Frame.CodecId.CodecInvalid; // 需要从数据中检测
            this.pesBuffer = ByteBuffer.allocate(1024 * 1024); // 1MB PES缓冲区
        }
        
        private static Frame.TrackType getTrackTypeFromStreamId(int streamId) {
            if (streamId >= AUDIO_STREAM_ID_MIN && streamId <= AUDIO_STREAM_ID_MAX) {
                return Frame.TrackType.TrackAudio;
            } else if (streamId >= VIDEO_STREAM_ID_MIN && streamId <= VIDEO_STREAM_ID_MAX) {
                return Frame.TrackType.TrackVideo;
            } else {
                return Frame.TrackType.TrackInvalid;
            }
        }
    }
    
    /**
     * 构造函数
     */
    public PSDecoder() {
        this.inputBuffer = ByteBuffer.allocate(64 * 1024); // 64KB输入缓冲区
        
        logger.debug("Created PSDecoder");
    }
    
    @Override
    public boolean input(byte[] data) {
        return input(data, 0, data.length);
    }
    
    @Override
    public boolean input(byte[] data, int offset, int length) {
        if (data == null || length <= 0 || !ready) {
            return false;
        }
        
        try {
            totalBytes.addAndGet(length);
            
            // 添加数据到输入缓冲区
            if (inputBuffer.remaining() < length) {
                // 压缩缓冲区
                inputBuffer.compact();
                
                // 如果还是不够空间，扩展缓冲区或清空
                if (inputBuffer.remaining() < length) {
                    logger.warn("Input buffer overflow, clearing buffer");
                    inputBuffer.clear();
                }
            }
            
            inputBuffer.put(data, offset, length);
            
            // 处理PS包
            processPS();
            
            return true;
            
        } catch (Exception e) {
            logger.error("Error processing PS data", e);
            errorPackets.incrementAndGet();
            return false;
        }
    }
    
    /**
     * 处理PS包
     */
    private void processPS() {
        inputBuffer.flip(); // 切换到读模式
        
        while (inputBuffer.remaining() >= 4) {
            // 查找起始码
            int startCode = findStartCode();
            if (startCode == -1) {
                break; // 没有找到起始码
            }
            
            // 处理不同类型的包
            if (startCode == PS_PACK_START_CODE) {
                processPSPackHeader();
            } else if (startCode == PS_SYSTEM_HEADER_START_CODE) {
                processPSSystemHeader();
            } else if (startCode == PS_PROGRAM_STREAM_MAP) {
                processPSProgramStreamMap();
            } else if (startCode == PS_PROGRAM_END_CODE) {
                processPSProgramEnd();
            } else if ((startCode & 0xFFFFFF00) == PES_START_CODE_PREFIX) {
                // PES包
                int streamId = startCode & 0xFF;
                processPESPacket(streamId);
            } else {
                // 未知包，跳过
                inputBuffer.get();
            }
        }
        
        inputBuffer.compact(); // 切换到写模式，保留未处理的数据
    }
    
    /**
     * 查找起始码
     * 
     * @return 起始码，如果没有找到返回-1
     */
    private int findStartCode() {
        while (inputBuffer.remaining() >= 4) {
            int position = inputBuffer.position();
            
            if (inputBuffer.get(position) == 0x00 &&
                inputBuffer.get(position + 1) == 0x00 &&
                inputBuffer.get(position + 2) == 0x01) {
                
                // 找到起始码
                int startCode = ((inputBuffer.get(position) & 0xFF) << 24) |
                               ((inputBuffer.get(position + 1) & 0xFF) << 16) |
                               ((inputBuffer.get(position + 2) & 0xFF) << 8) |
                               (inputBuffer.get(position + 3) & 0xFF);
                
                return startCode;
            } else {
                // 跳过一个字节继续查找
                inputBuffer.get();
            }
        }
        
        return -1;
    }
    
    /**
     * 处理PS包头
     */
    private void processPSPackHeader() {
        if (inputBuffer.remaining() < 14) {
            return; // 数据不够
        }
        
        // 跳过PS包头（最少14字节）
        inputBuffer.position(inputBuffer.position() + 4); // 跳过起始码
        
        // 读取包头长度信息
        byte[] header = new byte[10];
        inputBuffer.get(header);
        
        // 检查是否有填充字节
        if (inputBuffer.remaining() >= 1) {
            int stuffingLength = inputBuffer.get() & 0x07;
            if (inputBuffer.remaining() >= stuffingLength) {
                inputBuffer.position(inputBuffer.position() + stuffingLength);
            }
        }
        
        totalPackets.incrementAndGet();
        logger.debug("Processed PS pack header");
    }
    
    /**
     * 处理PS系统头
     */
    private void processPSSystemHeader() {
        if (inputBuffer.remaining() < 6) {
            return; // 数据不够
        }
        
        inputBuffer.position(inputBuffer.position() + 4); // 跳过起始码
        
        // 读取系统头长度
        int headerLength = ((inputBuffer.get() & 0xFF) << 8) | (inputBuffer.get() & 0xFF);
        
        if (inputBuffer.remaining() >= headerLength) {
            inputBuffer.position(inputBuffer.position() + headerLength);
            logger.debug("Processed PS system header, length: {}", headerLength);
        }
    }
    
    /**
     * 处理PS程序流映射
     */
    private void processPSProgramStreamMap() {
        if (inputBuffer.remaining() < 6) {
            return; // 数据不够
        }
        
        inputBuffer.position(inputBuffer.position() + 4); // 跳过起始码
        
        // 读取映射长度
        int mapLength = ((inputBuffer.get() & 0xFF) << 8) | (inputBuffer.get() & 0xFF);
        
        if (inputBuffer.remaining() >= mapLength) {
            inputBuffer.position(inputBuffer.position() + mapLength);
            logger.debug("Processed PS program stream map, length: {}", mapLength);
        }
    }
    
    /**
     * 处理PS程序结束
     */
    private void processPSProgramEnd() {
        inputBuffer.position(inputBuffer.position() + 4); // 跳过起始码
        logger.debug("Processed PS program end");
    }
    
    /**
     * 处理PES包
     * 
     * @param streamId 流ID
     */
    private void processPESPacket(int streamId) {
        if (inputBuffer.remaining() < 6) {
            return; // 数据不够
        }
        
        int startPosition = inputBuffer.position();
        inputBuffer.position(startPosition + 4); // 跳过起始码
        
        // 读取PES包长度
        int pesPacketLength = ((inputBuffer.get() & 0xFF) << 8) | (inputBuffer.get() & 0xFF);
        
        // 计算实际数据长度
        int actualLength = pesPacketLength;
        if (pesPacketLength == 0) {
            // 长度为0表示不限制长度，读取到下一个起始码
            actualLength = findNextStartCodeDistance();
            if (actualLength == -1) {
                // 没有找到下一个起始码，使用剩余所有数据
                actualLength = inputBuffer.remaining();
            }
        }
        
        if (inputBuffer.remaining() < actualLength) {
            // 数据不完整，回退位置
            inputBuffer.position(startPosition);
            return;
        }
        
        // 读取PES数据
        byte[] pesData = new byte[actualLength + 6]; // 包含起始码和长度字段
        inputBuffer.position(startPosition);
        inputBuffer.get(pesData);
        
        // 处理PES数据
        processPESData(streamId, pesData);
        
        totalPackets.incrementAndGet();
    }
    
    /**
     * 查找下一个起始码的距离
     * 
     * @return 距离，如果没有找到返回-1
     */
    private int findNextStartCodeDistance() {
        int currentPosition = inputBuffer.position();
        int distance = 0;
        
        while (inputBuffer.remaining() >= 4) {
            if (inputBuffer.get() == 0x00 &&
                inputBuffer.get() == 0x00 &&
                inputBuffer.get() == 0x01) {
                
                // 找到起始码
                inputBuffer.position(currentPosition);
                return distance;
            } else {
                // 回退2个字节继续查找
                inputBuffer.position(inputBuffer.position() - 2);
                distance++;
            }
        }
        
        inputBuffer.position(currentPosition);
        return -1;
    }
    
    /**
     * 处理PES数据
     * 
     * @param streamId 流ID
     * @param pesData PES数据
     */
    private void processPESData(int streamId, byte[] pesData) {
        if (pesData.length < 9) {
            return; // PES包太短
        }
        
        // 获取或创建流
        PSStream stream = streams.computeIfAbsent(streamId, PSStream::new);
        
        // 解析PES头部
        int pesHeaderDataLength = pesData[8] & 0xFF;
        int esDataOffset = 9 + pesHeaderDataLength;
        
        // 解析PTS/DTS
        parsePTSDTS(stream, pesData, 9, pesHeaderDataLength);
        
        // 提取ES数据
        if (esDataOffset < pesData.length) {
            byte[] esData = new byte[pesData.length - esDataOffset];
            System.arraycopy(pesData, esDataOffset, esData, 0, esData.length);
            
            // 检测编解码器类型
            if (stream.codecId == Frame.CodecId.CodecInvalid) {
                stream.codecId = detectCodecId(esData, stream.trackType);
                logger.debug("Detected codec for stream {}: {}", streamId, stream.codecId);
            }
            
            // 输出帧
            outputFrame(stream, esData);
        }
    }
    
    /**
     * 解析PTS/DTS
     */
    private void parsePTSDTS(PSStream stream, byte[] pesData, int offset, int length) {
        if (length < 5 || offset + 5 > pesData.length) {
            return;
        }
        
        int ptsFlags = (pesData[offset - 2] & 0xC0) >> 6;
        
        if (ptsFlags >= 2) {
            // 有PTS
            long pts = ((long)(pesData[offset] & 0x0E) << 29) |
                      ((long)(pesData[offset + 1] & 0xFF) << 22) |
                      ((long)(pesData[offset + 2] & 0xFE) << 14) |
                      ((long)(pesData[offset + 3] & 0xFF) << 7) |
                      ((long)(pesData[offset + 4] & 0xFE) >> 1);
            
            stream.lastPts = pts;
            
            if (ptsFlags == 3 && length >= 10 && offset + 10 <= pesData.length) {
                // 有DTS
                long dts = ((long)(pesData[offset + 5] & 0x0E) << 29) |
                          ((long)(pesData[offset + 6] & 0xFF) << 22) |
                          ((long)(pesData[offset + 7] & 0xFE) << 14) |
                          ((long)(pesData[offset + 8] & 0xFF) << 7) |
                          ((long)(pesData[offset + 9] & 0xFE) >> 1);
                
                stream.lastDts = dts;
            } else {
                stream.lastDts = pts;
            }
        }
    }
    
    /**
     * 检测编解码器ID
     * 
     * @param data ES数据
     * @param trackType 轨道类型
     * @return 编解码器ID
     */
    private Frame.CodecId detectCodecId(byte[] data, Frame.TrackType trackType) {
        if (data == null || data.length < 4) {
            return Frame.CodecId.CodecInvalid;
        }
        
        if (trackType == Frame.TrackType.TrackVideo) {
            // 检测H264
            if (isH264Data(data)) {
                return Frame.CodecId.CodecH264;
            }
            
            // 检测H265
            if (isH265Data(data)) {
                return Frame.CodecId.CodecH265;
            }
        } else if (trackType == Frame.TrackType.TrackAudio) {
            // 检测AAC
            if (isAACData(data)) {
                return Frame.CodecId.CodecAAC;
            }
        }
        
        return Frame.CodecId.CodecInvalid;
    }
    
    /**
     * 检测是否为H264数据
     */
    private boolean isH264Data(byte[] data) {
        // 查找H264起始码
        for (int i = 0; i <= data.length - 4; i++) {
            if (data[i] == 0x00 && data[i + 1] == 0x00 && 
                data[i + 2] == 0x00 && data[i + 3] == 0x01) {
                return true;
            } else if (i <= data.length - 3 && 
                      data[i] == 0x00 && data[i + 1] == 0x00 && data[i + 2] == 0x01) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * 检测是否为H265数据
     */
    private boolean isH265Data(byte[] data) {
        // 查找H265起始码
        for (int i = 0; i <= data.length - 5; i++) {
            if (data[i] == 0x00 && data[i + 1] == 0x00 && 
                data[i + 2] == 0x00 && data[i + 3] == 0x01) {
                int naluType = (data[i + 4] & 0x7E) >> 1;
                if (naluType >= 0 && naluType <= 40) {
                    return true;
                }
            }
        }
        return false;
    }
    
    /**
     * 检测是否为AAC数据
     */
    private boolean isAACData(byte[] data) {
        // 检测AAC ADTS头部
        if (data.length >= 2) {
            return (data[0] & 0xFF) == 0xFF && (data[1] & 0xF0) == 0xF0;
        }
        return false;
    }
    
    /**
     * 输出帧
     */
    private void outputFrame(PSStream stream, byte[] data) {
        if (onFrameCallback == null || data == null || data.length == 0) {
            return;
        }
        
        try {
            // 转换时间戳（90kHz -> 毫秒）
            long dts = stream.lastDts != -1 ? stream.lastDts / 90 : 0;
            long pts = stream.lastPts != -1 ? stream.lastPts / 90 : dts;
            
            // 创建帧
            FrameImpl frame = new FrameImpl(stream.codecId, data, dts, pts);
            frame.setIndex(stream.streamId);
            
            // 调用回调
            onFrameCallback.accept(frame);
            outputFrames.incrementAndGet();
            
            logger.debug("Output PS frame: streamId={}, codec={}, size={}, dts={}, pts={}", 
                        stream.streamId, stream.codecId, data.length, dts, pts);
            
        } catch (Exception e) {
            logger.error("Error outputting PS frame", e);
        }
    }
    
    @Override
    public void setOnFrame(Consumer<Frame> callback) {
        this.onFrameCallback = callback;
    }
    
    @Override
    public void flush() {
        reset();
    }
    
    @Override
    public String getDecoderName() {
        return "PSDecoder";
    }
    
    @Override
    public String getStatistics() {
        StringBuilder sb = new StringBuilder();
        sb.append("PSDecoder Statistics:\n");
        sb.append("  Total Packets: ").append(totalPackets.get()).append("\n");
        sb.append("  Total Bytes: ").append(totalBytes.get()).append("\n");
        sb.append("  Output Frames: ").append(outputFrames.get()).append("\n");
        sb.append("  Error Packets: ").append(errorPackets.get()).append("\n");
        sb.append("  Streams: ").append(streams.size()).append("\n");
        sb.append("  Ready: ").append(ready).append("\n");
        
        return sb.toString();
    }
    
    @Override
    public void reset() {
        inputBuffer.clear();
        streams.clear();
        ready = true;
        
        logger.debug("PSDecoder reset");
    }
    
    @Override
    public boolean isReady() {
        return ready;
    }
}
