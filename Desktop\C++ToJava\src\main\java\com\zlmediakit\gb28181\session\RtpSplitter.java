package com.zlmediakit.gb28181.session;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.nio.ByteBuffer;

/**
 * RTP包分割器
 * 基于ZLMediaKit的RtpSplitter实现
 * 用于TCP模式下的RTP包分割和重组
 * 
 * <AUTHOR> Java Port
 */
public class RtpSplitter {
    private static final Logger logger = LoggerFactory.getLogger(RtpSplitter.class);
    
    // EHOME协议相关常量
    private static final int EHOME_MAGIC = 0x484F4D45; // "HOME"
    private static final int EHOME_HEADER_SIZE = 8;
    
    // 缓冲区
    private ByteBuffer buffer;
    private static final int MAX_BUFFER_SIZE = 64 * 1024; // 64KB
    
    // 回调接口
    public interface OnRtpPacket {
        void onRtpPacket(byte[] data, int offset, int length);
    }
    
    private OnRtpPacket onRtpPacketCallback;
    
    /**
     * 构造函数
     */
    public RtpSplitter() {
        this.buffer = ByteBuffer.allocate(MAX_BUFFER_SIZE);
    }
    
    /**
     * 设置RTP包回调
     * 
     * @param callback 回调函数
     */
    public void setOnRtpPacket(OnRtpPacket callback) {
        this.onRtpPacketCallback = callback;
    }
    
    /**
     * 输入数据
     * 
     * @param data 输入数据
     */
    public void input(byte[] data) {
        if (data == null || data.length == 0) {
            return;
        }
        
        input(data, 0, data.length);
    }
    
    /**
     * 输入数据
     * 
     * @param data 输入数据
     * @param offset 偏移量
     * @param length 长度
     */
    public void input(byte[] data, int offset, int length) {
        if (data == null || length <= 0) {
            return;
        }
        
        // 检查缓冲区空间
        if (buffer.remaining() < length) {
            // 压缩缓冲区
            compactBuffer();
            
            // 如果还是不够空间，清空缓冲区
            if (buffer.remaining() < length) {
                logger.warn("Buffer overflow, clearing buffer");
                buffer.clear();
            }
        }
        
        // 添加数据到缓冲区
        buffer.put(data, offset, length);
        
        // 处理缓冲区中的数据
        processBuffer();
    }
    
    /**
     * 处理缓冲区中的数据
     */
    private void processBuffer() {
        buffer.flip(); // 切换到读模式
        
        while (buffer.remaining() >= 4) {
            int startPosition = buffer.position();
            
            // 尝试解析RTP包
            int packetLength = tryParseRtpPacket();
            if (packetLength > 0) {
                // 找到完整的RTP包
                if (buffer.remaining() >= packetLength) {
                    // 提取RTP包数据
                    byte[] rtpData = new byte[packetLength];
                    buffer.get(rtpData);
                    
                    // 调用回调
                    if (onRtpPacketCallback != null) {
                        onRtpPacketCallback.onRtpPacket(rtpData, 0, packetLength);
                    }
                    
                    continue; // 继续处理下一个包
                } else {
                    // 数据不完整，等待更多数据
                    buffer.position(startPosition);
                    break;
                }
            } else {
                // 无法解析，跳过一个字节
                buffer.get();
            }
        }
        
        buffer.compact(); // 切换到写模式，保留未处理的数据
    }
    
    /**
     * 尝试解析RTP包
     * 
     * @return RTP包长度，如果无法解析返回-1
     */
    private int tryParseRtpPacket() {
        if (buffer.remaining() < 4) {
            return -1;
        }
        
        int position = buffer.position();
        
        // 检查是否为EHOME协议
        int magic = buffer.getInt(position);
        if (magic == EHOME_MAGIC) {
            return tryParseEhomePacket();
        }
        
        // 检查是否为标准RTP包
        return tryParseStandardRtp();
    }
    
    /**
     * 尝试解析EHOME协议包
     * 
     * @return 包长度
     */
    private int tryParseEhomePacket() {
        if (buffer.remaining() < EHOME_HEADER_SIZE) {
            return -1;
        }
        
        int position = buffer.position();
        
        // 跳过魔数
        buffer.position(position + 4);
        
        // 读取长度字段
        int length = buffer.getInt();
        
        // 恢复位置
        buffer.position(position);
        
        // 验证长度
        if (length < EHOME_HEADER_SIZE || length > MAX_BUFFER_SIZE) {
            return -1;
        }
        
        return length;
    }
    
    /**
     * 尝试解析标准RTP包
     * 
     * @return 包长度
     */
    private int tryParseStandardRtp() {
        if (buffer.remaining() < 12) { // RTP最小头部长度
            return -1;
        }
        
        int position = buffer.position();
        
        // 读取RTP头部第一个字节
        byte firstByte = buffer.get(position);
        
        // 检查版本号（前2位应该是2）
        int version = (firstByte & 0xC0) >> 6;
        if (version != 2) {
            return -1;
        }
        
        // 检查填充位
        boolean padding = (firstByte & 0x20) != 0;
        
        // 检查扩展位
        boolean extension = (firstByte & 0x10) != 0;
        
        // 检查CSRC计数
        int csrcCount = firstByte & 0x0F;
        
        // 计算基本头部长度
        int headerLength = 12 + csrcCount * 4;
        
        if (buffer.remaining() < headerLength) {
            return -1;
        }
        
        // 如果有扩展头
        if (extension) {
            if (buffer.remaining() < headerLength + 4) {
                return -1;
            }
            
            // 读取扩展长度
            int extLength = buffer.getShort(position + headerLength + 2) & 0xFFFF;
            headerLength += 4 + extLength * 4;
            
            if (buffer.remaining() < headerLength) {
                return -1;
            }
        }
        
        // 读取负载长度（这里简化处理，假设剩余数据都是负载）
        int payloadLength = buffer.remaining() - headerLength;
        
        // 如果有填充，需要减去填充长度
        if (padding && payloadLength > 0) {
            byte paddingLength = buffer.get(position + buffer.remaining() - 1);
            payloadLength -= paddingLength;
        }
        
        // 验证包长度
        int totalLength = headerLength + payloadLength;
        if (totalLength < 12 || totalLength > MAX_BUFFER_SIZE) {
            return -1;
        }
        
        return totalLength;
    }
    
    /**
     * 压缩缓冲区
     */
    private void compactBuffer() {
        if (buffer.position() > 0) {
            buffer.compact();
        }
    }
    
    /**
     * 重置分割器
     */
    public void reset() {
        buffer.clear();
    }
    
    /**
     * 获取缓冲区使用情况
     * 
     * @return 使用的字节数
     */
    public int getBufferUsage() {
        return buffer.position();
    }
}
