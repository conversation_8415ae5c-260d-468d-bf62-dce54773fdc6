package com.zlmediakit.gb28181.network;

import com.zlmediakit.gb28181.session.RtpSession;
import com.zlmediakit.gb28181.session.RtpSplitter;
import io.netty.buffer.ByteBuf;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.ChannelInboundHandlerAdapter;
import io.netty.channel.ChannelInitializer;
import io.netty.channel.socket.SocketChannel;


import java.net.InetSocketAddress;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * TCP RTP数据接收器
 * 基于ZLMediaKit的TCP RTP处理逻辑
 * 支持RTP包分割和重组
 * 
 * <AUTHOR> Java Port
 */
public class TcpRtpReceiver extends ChannelInitializer<SocketChannel> {
    private static final Logger logger = LoggerFactory.getLogger(TcpRtpReceiver.class);
    
    private final RtpServer rtpServer;
    
    /**
     * 构造函数
     * 
     * @param rtpServer RTP服务器实例
     */
    public TcpRtpReceiver(RtpServer rtpServer) {
        this.rtpServer = rtpServer;
    }
    
    @Override
    protected void initChannel(SocketChannel ch) throws Exception {
        ch.pipeline().addLast(new TcpRtpHandler(rtpServer));
    }
    
    /**
     * TCP RTP处理器
     */
    private static class TcpRtpHandler extends ChannelInboundHandlerAdapter {
        private static final Logger logger = LoggerFactory.getLogger(TcpRtpHandler.class);
        
        private final RtpServer rtpServer;
        private RtpSession session;
        private RtpSplitter rtpSplitter;
        
        public TcpRtpHandler(RtpServer rtpServer) {
            this.rtpServer = rtpServer;
        }
        
        @Override
        public void channelActive(ChannelHandlerContext ctx) throws Exception {
            InetSocketAddress remoteAddress = (InetSocketAddress) ctx.channel().remoteAddress();
            logger.info("TCP RTP connection established from {}", remoteAddress);
            
            // 创建会话
            session = rtpServer.createSession(remoteAddress, false);
            
            // 创建RTP分割器
            rtpSplitter = new RtpSplitter();
            rtpSplitter.setOnRtpPacket((data, offset, length) -> {
                try {
                    // 处理RTP包
                    byte[] rtpData = new byte[length];
                    System.arraycopy(data, offset, rtpData, 0, length);
                    session.inputRtp(rtpData, remoteAddress);
                } catch (Exception e) {
                    logger.error("Error processing TCP RTP packet", e);
                }
            });
            
            super.channelActive(ctx);
        }
        
        @Override
        public void channelRead(ChannelHandlerContext ctx, Object msg) throws Exception {
            if (msg instanceof ByteBuf) {
                ByteBuf buffer = (ByteBuf) msg;
                
                try {
                    // 将数据传递给RTP分割器
                    byte[] data = new byte[buffer.readableBytes()];
                    buffer.readBytes(data);
                    
                    if (rtpSplitter != null) {
                        rtpSplitter.input(data);
                    }
                    
                } catch (Exception e) {
                    logger.error("Error processing TCP data", e);
                } finally {
                    buffer.release();
                }
            }
        }
        
        @Override
        public void channelInactive(ChannelHandlerContext ctx) throws Exception {
            InetSocketAddress remoteAddress = (InetSocketAddress) ctx.channel().remoteAddress();
            logger.info("TCP RTP connection closed from {}", remoteAddress);
            
            // 清理会话
            if (session != null) {
                session.close();
                session = null;
            }
            
            rtpSplitter = null;
            
            super.channelInactive(ctx);
        }
        
        @Override
        public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) throws Exception {
            logger.error("TCP RTP handler exception", cause);
            ctx.close();
        }
    }
}
