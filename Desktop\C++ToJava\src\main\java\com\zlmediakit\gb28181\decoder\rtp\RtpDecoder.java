package com.zlmediakit.gb28181.decoder.rtp;

import com.zlmediakit.gb28181.media.Frame;
import com.zlmediakit.gb28181.rtp.RtpPacket;

import java.util.function.Consumer;

/**
 * RTP解码器接口
 * 基于ZLMediaKit的RtpDecoder实现
 * 
 * <AUTHOR> Java Port
 */
public interface RtpDecoder {
    
    /**
     * 输入RTP包
     * 
     * @param packet RTP包
     * @return 是否处理成功
     */
    boolean inputRtp(RtpPacket packet);
    
    /**
     * 设置帧回调
     * 
     * @param callback 帧回调函数
     */
    void setOnFrame(Consumer<Frame> callback);
    
    /**
     * 刷新缓存
     */
    void flush();
    
    /**
     * 获取编解码器ID
     *
     * @return 编解码器ID
     */
    Frame.CodecId getCodecId();

    /**
     * 获取解码器名称
     *
     * @return 解码器名称
     */
    String getDecoderName();

    /**
     * 获取统计信息
     *
     * @return 统计信息
     */
    String getStatistics();

    /**
     * 重置解码器状态
     */
    void reset();

    /**
     * 检查解码器是否就绪
     *
     * @return boolean
     */
    boolean isReady();
}
