package com.zlmediakit.gb28181.rtp;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import static org.junit.jupiter.api.Assertions.*;

/**
 * RtpHeader单元测试
 * 
 * <AUTHOR> Java Port
 */
public class RtpHeaderTest {
    
    private byte[] validRtpPacket;
    private byte[] invalidRtpPacket;
    
    @BeforeEach
    void setUp() {
        // 创建一个有效的RTP包
        validRtpPacket = new byte[20];
        validRtpPacket[0] = (byte) 0x80; // V=2, P=0, X=0, CC=0
        validRtpPacket[1] = (byte) 0x60; // M=0, PT=96
        validRtpPacket[2] = 0x12; // 序列号高字节
        validRtpPacket[3] = 0x34; // 序列号低字节
        validRtpPacket[4] = 0x56; // 时间戳字节1
        validRtpPacket[5] = 0x78; // 时间戳字节2
        validRtpPacket[6] = (byte) 0x9A; // 时间戳字节3
        validRtpPacket[7] = (byte) 0xBC; // 时间戳字节4
        validRtpPacket[8] = (byte) 0xDE; // SSRC字节1
        validRtpPacket[9] = (byte) 0xF0; // SSRC字节2
        validRtpPacket[10] = 0x12; // SSRC字节3
        validRtpPacket[11] = 0x34; // SSRC字节4
        
        // 创建一个无效的RTP包（版本号错误）
        invalidRtpPacket = new byte[12];
        invalidRtpPacket[0] = (byte) 0x40; // V=1（错误版本）
    }
    
    @Test
    void testValidRtpPacketDetection() {
        assertTrue(RtpHeader.isValidRtpPacket(validRtpPacket, 0, validRtpPacket.length));
        assertFalse(RtpHeader.isValidRtpPacket(invalidRtpPacket, 0, invalidRtpPacket.length));
        assertFalse(RtpHeader.isValidRtpPacket(null, 0, 0));
        assertFalse(RtpHeader.isValidRtpPacket(new byte[8], 0, 8)); // 太短
    }
    
    @Test
    void testRtpHeaderParsing() {
        RtpHeader header = new RtpHeader(validRtpPacket, 0);
        
        assertEquals(2, header.getVersion());
        assertFalse(header.isPadding());
        assertFalse(header.isExtension());
        assertEquals(0, header.getCsrcCount());
        assertFalse(header.isMarker());
        assertEquals(96, header.getPayloadType());
        assertEquals(0x1234, header.getSequenceNumber());
        assertEquals(0x56789ABCL, header.getTimestamp());
        assertEquals(0xDEF01234L, header.getSsrc());
    }
    
    @Test
    void testRtpHeaderWithPadding() {
        byte[] paddedPacket = validRtpPacket.clone();
        paddedPacket[0] = (byte) 0xA0; // 设置P=1
        
        RtpHeader header = new RtpHeader(paddedPacket, 0);
        assertTrue(header.isPadding());
    }
    
    @Test
    void testRtpHeaderWithExtension() {
        byte[] extendedPacket = validRtpPacket.clone();
        extendedPacket[0] = (byte) 0x90; // 设置X=1
        
        RtpHeader header = new RtpHeader(extendedPacket, 0);
        assertTrue(header.isExtension());
    }
    
    @Test
    void testRtpHeaderWithCsrc() {
        byte[] csrcPacket = new byte[28]; // 12字节头部 + 4个CSRC
        System.arraycopy(validRtpPacket, 0, csrcPacket, 0, 12);
        csrcPacket[0] = (byte) 0x84; // 设置CC=4
        
        RtpHeader header = new RtpHeader(csrcPacket, 0);
        assertEquals(4, header.getCsrcCount());
    }
    
    @Test
    void testRtpHeaderWithMarker() {
        byte[] markerPacket = validRtpPacket.clone();
        markerPacket[1] = (byte) 0xE0; // 设置M=1, PT=96
        
        RtpHeader header = new RtpHeader(markerPacket, 0);
        assertTrue(header.isMarker());
        assertEquals(96, header.getPayloadType());
    }
    
    @Test
    void testGetHeaderLength() {
        // 基本头部长度
        RtpHeader basicHeader = new RtpHeader(validRtpPacket, 0);
        assertEquals(12, basicHeader.getHeaderLength());
        
        // 带CSRC的头部
        byte[] csrcPacket = new byte[28];
        System.arraycopy(validRtpPacket, 0, csrcPacket, 0, 12);
        csrcPacket[0] = (byte) 0x82; // CC=2
        RtpHeader csrcHeader = new RtpHeader(csrcPacket, 0);
        assertEquals(20, csrcHeader.getHeaderLength()); // 12 + 2*4
    }
    
    @Test
    void testGetPayloadLength() {
        RtpHeader header = new RtpHeader(validRtpPacket, 0);
        assertEquals(8, header.getPayloadLength()); // 20 - 12

        // 测试带CSRC的情况
        byte[] csrcPacket = new byte[28];
        System.arraycopy(validRtpPacket, 0, csrcPacket, 0, 12);
        csrcPacket[0] = (byte) 0x82; // CC=2
        RtpHeader csrcHeader = new RtpHeader(csrcPacket, 0);
        assertEquals(8, csrcHeader.getPayloadLength()); // 28 - 20
    }
    
    @Test
    void testToString() {
        RtpHeader header = new RtpHeader(validRtpPacket, 0);
        String str = header.toString();

        assertNotNull(str);
        assertTrue(str.length() > 0);
        assertTrue(str.contains("RtpHeader"));
        // 基本验证toString不为空且包含类名
    }
    
    @Test
    void testInvalidPacketHandling() {
        // 测试空包
        assertThrows(IllegalArgumentException.class, () -> {
            new RtpHeader(null, 0);
        });
        
        // 测试太短的包
        assertThrows(IllegalArgumentException.class, () -> {
            new RtpHeader(new byte[8], 0);
        });
        
        // 测试偏移量超出范围
        assertThrows(IllegalArgumentException.class, () -> {
            new RtpHeader(validRtpPacket, validRtpPacket.length);
        });
    }
    
    @Test
    void testDifferentPayloadTypes() {
        for (int pt = 0; pt < 128; pt++) {
            byte[] packet = validRtpPacket.clone();
            packet[1] = (byte) (pt & 0x7F); // M=0，设置PT

            RtpHeader header = new RtpHeader(packet, 0);
            assertEquals(pt, header.getPayloadType());
        }
    }
    
    @Test
    void testSequenceNumberWrap() {
        // 测试序列号边界值
        byte[] packet = validRtpPacket.clone();
        
        // 最大值
        packet[2] = (byte) 0xFF;
        packet[3] = (byte) 0xFF;
        RtpHeader header = new RtpHeader(packet, 0);
        assertEquals(65535, header.getSequenceNumber());
        
        // 最小值
        packet[2] = 0x00;
        packet[3] = 0x00;
        header = new RtpHeader(packet, 0);
        assertEquals(0, header.getSequenceNumber());
    }
    
    @Test
    void testTimestampValues() {
        byte[] packet = validRtpPacket.clone();
        
        // 测试最大时间戳
        packet[4] = (byte) 0xFF;
        packet[5] = (byte) 0xFF;
        packet[6] = (byte) 0xFF;
        packet[7] = (byte) 0xFF;
        
        RtpHeader header = new RtpHeader(packet, 0);
        assertEquals(0xFFFFFFFFL, header.getTimestamp());
    }
}
