package com.zlmediakit.gb28181.decoder.rtp;

import com.zlmediakit.gb28181.media.Frame;
import com.zlmediakit.gb28181.media.FrameImpl;
import com.zlmediakit.gb28181.rtp.RtpPacket;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.nio.ByteBuffer;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Consumer;

/**
 * H264 RTP解码器
 * 基于ZLMediaKit的H264RtpDecoder实现
 * 支持RFC 6184标准的H264 RTP负载格式
 * 
 * <AUTHOR> Java Port
 */
public class H264RtpDecoder implements RtpDecoder {
    private static final Logger logger = LoggerFactory.getLogger(H264RtpDecoder.class);
    
    // H264 NALU类型
    private static final int NALU_TYPE_SINGLE = 1;   // 单一NALU
    private static final int NALU_TYPE_STAP_A = 24;  // 单时间聚合包A
    private static final int NALU_TYPE_STAP_B = 25;  // 单时间聚合包B
    private static final int NALU_TYPE_MTAP16 = 26;  // 多时间聚合包16
    private static final int NALU_TYPE_MTAP24 = 27;  // 多时间聚合包24
    private static final int NALU_TYPE_FU_A = 28;    // 分片单元A
    private static final int NALU_TYPE_FU_B = 29;    // 分片单元B
    
    // H264起始码
    private static final byte[] START_CODE = {0x00, 0x00, 0x00, 0x01};
    
    // 缓冲区
    private final ByteBuffer frameBuffer;
    private final int maxFrameSize;
    
    // 回调函数
    private Consumer<Frame> onFrameCallback;
    
    // 状态变量
    private long lastTimestamp = -1;
    private boolean fuStarted = false;  // FU分片是否开始
    private int fuSeq = -1;             // FU分片序列号
    private boolean ready = true;
    
    // 统计信息
    private final AtomicLong totalPackets = new AtomicLong(0);
    private final AtomicLong totalBytes = new AtomicLong(0);
    private final AtomicLong outputFrames = new AtomicLong(0);
    private final AtomicLong fuPackets = new AtomicLong(0);
    private final AtomicLong stapPackets = new AtomicLong(0);
    
    /**
     * 构造函数
     * 
     * @param maxFrameSize 最大帧大小
     */
    public H264RtpDecoder(int maxFrameSize) {
        this.maxFrameSize = maxFrameSize;
        this.frameBuffer = ByteBuffer.allocate(maxFrameSize);
        
        logger.debug("Created H264RtpDecoder with max frame size: {}", maxFrameSize);
    }
    
    /**
     * 默认构造函数
     */
    public H264RtpDecoder() {
        this(2 * 1024 * 1024); // 2MB默认最大帧大小
    }
    
    @Override
    public boolean inputRtp(RtpPacket packet) {
        if (packet == null || !ready) {
            return false;
        }
        
        try {
            totalPackets.incrementAndGet();
            
            byte[] payload = packet.getPayload();
            if (payload == null || payload.length == 0) {
                return true; // 空负载，跳过
            }
            
            totalBytes.addAndGet(payload.length);
            
            // 检查时间戳变化
            long timestamp = packet.getTimestamp();
            if (lastTimestamp != -1 && lastTimestamp != timestamp) {
                // 时间戳变化，输出之前的帧
                flushFrame(lastTimestamp);
            }
            
            // 解析NALU类型
            int naluType = payload[0] & 0x1F;
            
            switch (naluType) {
                case NALU_TYPE_STAP_A:
                    stapPackets.incrementAndGet();
                    return processStapA(payload, timestamp);
                    
                case NALU_TYPE_FU_A:
                    fuPackets.incrementAndGet();
                    return processFuA(payload, packet.getSequenceNumber(), timestamp);
                    
                default:
                    // 单一NALU包
                    return processSingleNalu(payload, timestamp);
            }
            
        } catch (Exception e) {
            logger.error("Error processing H264 RTP packet", e);
            return false;
        }
    }
    
    /**
     * 处理STAP-A包（单时间聚合包A）
     * 
     * @param payload 负载数据
     * @param timestamp 时间戳
     * @return 是否处理成功
     */
    private boolean processStapA(byte[] payload, long timestamp) {
        if (payload.length < 2) {
            return false;
        }
        
        int offset = 1; // 跳过STAP-A头部
        
        while (offset + 2 <= payload.length) {
            // 读取NALU长度
            int naluLength = ((payload[offset] & 0xFF) << 8) | (payload[offset + 1] & 0xFF);
            offset += 2;
            
            if (offset + naluLength > payload.length) {
                logger.warn("Invalid NALU length in STAP-A: {}", naluLength);
                break;
            }
            
            // 添加起始码
            addStartCode();
            
            // 添加NALU数据
            if (frameBuffer.remaining() >= naluLength) {
                frameBuffer.put(payload, offset, naluLength);
            } else {
                logger.warn("Frame buffer overflow in STAP-A");
                return false;
            }
            
            offset += naluLength;
        }
        
        lastTimestamp = timestamp;
        return true;
    }
    
    /**
     * 处理FU-A包（分片单元A）
     * 
     * @param payload 负载数据
     * @param seq 序列号
     * @param timestamp 时间戳
     * @return 是否处理成功
     */
    private boolean processFuA(byte[] payload, int seq, long timestamp) {
        if (payload.length < 2) {
            return false;
        }
        
        byte fuIndicator = payload[0];
        byte fuHeader = payload[1];
        
        boolean start = (fuHeader & 0x80) != 0;
        boolean end = (fuHeader & 0x40) != 0;
        int naluType = fuHeader & 0x1F;
        
        if (start) {
            // FU开始
            if (fuStarted) {
                // 上一个FU没有结束，输出不完整的帧
                logger.warn("FU-A start without previous end");
                flushFrame(lastTimestamp);
            }
            
            fuStarted = true;
            fuSeq = seq;
            
            // 添加起始码
            addStartCode();
            
            // 重构NALU头部
            byte naluHeader = (byte) ((fuIndicator & 0xE0) | naluType);
            frameBuffer.put(naluHeader);
            
            // 添加负载数据
            if (frameBuffer.remaining() >= payload.length - 2) {
                frameBuffer.put(payload, 2, payload.length - 2);
            } else {
                logger.warn("Frame buffer overflow in FU-A start");
                fuStarted = false;
                return false;
            }
            
        } else if (fuStarted) {
            // FU中间或结束
            
            // 检查序列号连续性
            int expectedSeq = (fuSeq + 1) & 0xFFFF;
            if (seq != expectedSeq) {
                logger.warn("FU-A sequence discontinuity: expected={}, actual={}", expectedSeq, seq);
                fuStarted = false;
                frameBuffer.clear();
                return false;
            }
            
            fuSeq = seq;
            
            // 添加负载数据
            if (frameBuffer.remaining() >= payload.length - 2) {
                frameBuffer.put(payload, 2, payload.length - 2);
            } else {
                logger.warn("Frame buffer overflow in FU-A continuation");
                fuStarted = false;
                frameBuffer.clear();
                return false;
            }
            
            if (end) {
                // FU结束，输出帧
                fuStarted = false;
                flushFrame(timestamp);
            }
            
        } else {
            // 收到FU中间包但没有开始包
            logger.warn("FU-A continuation without start");
            return false;
        }
        
        lastTimestamp = timestamp;
        return true;
    }
    
    /**
     * 处理单一NALU包
     * 
     * @param payload 负载数据
     * @param timestamp 时间戳
     * @return 是否处理成功
     */
    private boolean processSingleNalu(byte[] payload, long timestamp) {
        // 添加起始码
        addStartCode();
        
        // 添加NALU数据
        if (frameBuffer.remaining() >= payload.length) {
            frameBuffer.put(payload);
        } else {
            logger.warn("Frame buffer overflow in single NALU");
            return false;
        }
        
        lastTimestamp = timestamp;
        return true;
    }
    
    /**
     * 添加H264起始码
     */
    private void addStartCode() {
        if (frameBuffer.remaining() >= START_CODE.length) {
            frameBuffer.put(START_CODE);
        }
    }
    
    /**
     * 刷新当前帧
     * 
     * @param timestamp 时间戳
     */
    private void flushFrame(long timestamp) {
        if (frameBuffer.position() == 0) {
            return; // 没有数据
        }
        
        // 创建帧数据
        byte[] frameData = new byte[frameBuffer.position()];
        frameBuffer.flip();
        frameBuffer.get(frameData);
        frameBuffer.clear();
        
        // 输出帧
        outputFrame(frameData, timestamp);
    }
    
    /**
     * 输出H264帧
     * 
     * @param data 帧数据
     * @param timestamp RTP时间戳
     */
    private void outputFrame(byte[] data, long timestamp) {
        if (onFrameCallback == null || data == null || data.length == 0) {
            return;
        }
        
        try {
            // 转换时间戳（假设90kHz时钟）
            long dts = timestamp * 1000 / 90000;
            long pts = dts;
            
            // 创建H264帧
            FrameImpl frame = new FrameImpl(Frame.CodecId.CodecH264, data, dts, pts);
            frame.autoDetectKeyFrame(); // 自动检测关键帧
            frame.setPrefixSize(START_CODE.length);
            
            // 调用回调
            onFrameCallback.accept(frame);
            outputFrames.incrementAndGet();
            
            logger.debug("Output H264 frame: size={}, timestamp={}, dts={}, keyFrame={}", 
                        data.length, timestamp, dts, frame.isKeyFrame());
            
        } catch (Exception e) {
            logger.error("Error outputting H264 frame", e);
        }
    }
    
    @Override
    public void setOnFrame(Consumer<Frame> callback) {
        this.onFrameCallback = callback;
    }
    
    @Override
    public void flush() {
        if (lastTimestamp != -1) {
            flushFrame(lastTimestamp);
        }
        reset();
    }
    
    @Override
    public Frame.CodecId getCodecId() {
        return Frame.CodecId.CodecH264;
    }
    
    @Override
    public String getDecoderName() {
        return "H264RtpDecoder";
    }
    
    @Override
    public String getStatistics() {
        StringBuilder sb = new StringBuilder();
        sb.append("H264RtpDecoder Statistics:\n");
        sb.append("  Total Packets: ").append(totalPackets.get()).append("\n");
        sb.append("  Total Bytes: ").append(totalBytes.get()).append("\n");
        sb.append("  Output Frames: ").append(outputFrames.get()).append("\n");
        sb.append("  FU Packets: ").append(fuPackets.get()).append("\n");
        sb.append("  STAP Packets: ").append(stapPackets.get()).append("\n");
        sb.append("  Buffer Usage: ").append(frameBuffer.position()).append("/").append(maxFrameSize).append("\n");
        sb.append("  FU Started: ").append(fuStarted).append("\n");
        sb.append("  Ready: ").append(ready).append("\n");
        
        return sb.toString();
    }
    
    @Override
    public void reset() {
        frameBuffer.clear();
        lastTimestamp = -1;
        fuStarted = false;
        fuSeq = -1;
        ready = true;
        
        logger.debug("H264RtpDecoder reset");
    }
    
    @Override
    public boolean isReady() {
        return ready;
    }
}
