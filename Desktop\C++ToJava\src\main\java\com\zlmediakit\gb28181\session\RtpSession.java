package com.zlmediakit.gb28181.session;

import com.zlmediakit.gb28181.media.Frame;
import com.zlmediakit.gb28181.media.MediaTuple;
import com.zlmediakit.gb28181.media.MediaSink;
import com.zlmediakit.gb28181.media.MediaSinkImpl;
import com.zlmediakit.gb28181.protocol.GB28181Process;
import com.zlmediakit.gb28181.rtp.RtpHeader;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.InetSocketAddress;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Consumer;

/**
 * RTP会话
 * 基于ZLMediaKit的RtpSession实现
 * 管理单个RTP流的接收和处理
 * 
 * <AUTHOR> Java Port
 */
public class RtpSession {
    private static final Logger logger = LoggerFactory.getLogger(RtpSession.class);
    
    // 会话信息
    private final String sessionId;
    private final MediaTuple mediaTuple;
    private final InetSocketAddress remoteAddress;
    private final boolean isUdp;
    
    // 状态管理
    private final AtomicBoolean active = new AtomicBoolean(true);
    private final AtomicLong lastFrameTime = new AtomicLong(System.currentTimeMillis());
    private final AtomicLong totalBytes = new AtomicLong(0);
    
    // 配置参数
    private int timeoutSec = 15;
    private long ssrc = 0;
    private int onlyTrack = 0; // 0=all, 1=audio, 2=video
    
    // 处理器
    private GB28181Process gb28181Process;

    // 媒体接收器
    private MediaSink mediaSink;

    // 回调函数
    private Consumer<Frame> onFrameCallback;
    private Consumer<Exception> onDetachCallback;
    
    // RTP分割器（TCP模式使用）
    private RtpSplitter rtpSplitter;
    
    /**
     * 构造函数
     * 
     * @param mediaTuple 媒体元组
     * @param remoteAddress 远程地址
     * @param isUdp 是否UDP模式
     */
    public RtpSession(MediaTuple mediaTuple, InetSocketAddress remoteAddress, boolean isUdp) {
        this.mediaTuple = mediaTuple;
        this.remoteAddress = remoteAddress;
        this.isUdp = isUdp;
        this.sessionId = generateSessionId();
        
        // 如果是TCP模式，创建RTP分割器
        if (!isUdp) {
            this.rtpSplitter = new RtpSplitter();
            this.rtpSplitter.setOnRtpPacket(this::processRtpPacket);
        }

        // 创建媒体接收器
        this.mediaSink = new MediaSinkImpl();

        logger.debug("Created RTP session: {}", sessionId);
    }
    
    /**
     * 输入RTP数据
     * 
     * @param data RTP数据
     * @param sourceAddress 源地址
     */
    public void inputRtp(byte[] data, InetSocketAddress sourceAddress) {
        if (!active.get()) {
            return;
        }
        
        if (data == null || data.length < 12) {
            logger.debug("Invalid RTP packet size: {}", data != null ? data.length : 0);
            return;
        }
        
        try {
            // 更新统计信息
            totalBytes.addAndGet(data.length);
            lastFrameTime.set(System.currentTimeMillis());
            
            if (isUdp) {
                // UDP模式直接处理
                processRtpPacket(data, 0, data.length);
            } else {
                // TCP模式通过分割器处理
                if (rtpSplitter != null) {
                    rtpSplitter.input(data);
                }
            }
            
        } catch (Exception e) {
            logger.error("Error processing RTP packet in session {}", sessionId, e);
            onDetach(e);
        }
    }
    
    /**
     * 处理RTP包
     * 
     * @param data 数据
     * @param offset 偏移量
     * @param length 长度
     */
    private void processRtpPacket(byte[] data, int offset, int length) {
        if (!active.get()) {
            return;
        }
        
        // 验证RTP包
        if (!isValidRtpPacket(data, offset, length)) {
            logger.debug("Invalid RTP packet in session {}", sessionId);
            return;
        }
        
        // 解析RTP头部
        RtpHeader rtpHeader = new RtpHeader(data, offset);
        
        // SSRC验证
        if (ssrc != 0 && rtpHeader.getSsrc() != ssrc) {
            logger.debug("SSRC mismatch in session {}: expected={}, actual={}", 
                        sessionId, ssrc, rtpHeader.getSsrc());
            return;
        }
        
        // 创建GB28181处理器（延迟创建）
        if (gb28181Process == null) {
            createGB28181Process();
        }
        
        // 处理RTP包
        if (gb28181Process != null) {
            gb28181Process.inputRtp(isUdp, data, offset, length);
        }
    }
    
    /**
     * 创建GB28181处理器
     */
    private void createGB28181Process() {
        try {
            gb28181Process = new GB28181Process(mediaTuple, this::onFrame);
            logger.debug("Created GB28181 process for session {}", sessionId);
        } catch (Exception e) {
            logger.error("Failed to create GB28181 process for session {}", sessionId, e);
        }
    }
    
    /**
     * 帧回调处理
     * 
     * @param frame 媒体帧
     */
    private void onFrame(Frame frame) {
        if (!active.get()) {
            return;
        }
        
        try {
            // 轨道过滤
            if (onlyTrack != 0) {
                Frame.TrackType trackType = frame.getTrackType();
                if ((onlyTrack == 1 && trackType != Frame.TrackType.TrackAudio) ||
                    (onlyTrack == 2 && trackType != Frame.TrackType.TrackVideo)) {
                    return;
                }
            }
            
            // 输入到媒体接收器
            if (mediaSink != null) {
                mediaSink.inputFrame(frame);
            }

            // 调用外部回调
            if (onFrameCallback != null) {
                onFrameCallback.accept(frame);
            }
            
        } catch (Exception e) {
            logger.error("Error in frame callback for session {}", sessionId, e);
        }
    }
    
    /**
     * 验证RTP包
     * 
     * @param data 数据
     * @param offset 偏移量
     * @param length 长度
     * @return boolean
     */
    private boolean isValidRtpPacket(byte[] data, int offset, int length) {
        if (length < 12) {
            return false;
        }
        
        // 检查版本号
        int version = (data[offset] & 0xC0) >> 6;
        return version == 2;
    }
    
    /**
     * 生成会话ID
     * 
     * @return 会话ID
     */
    private String generateSessionId() {
        return String.format("%s:%d_%s_%s", 
                           remoteAddress.getHostString(), 
                           remoteAddress.getPort(), 
                           isUdp ? "udp" : "tcp",
                           mediaTuple.getStream());
    }
    
    /**
     * 检查会话是否存活
     * 
     * @param currentTime 当前时间
     * @return boolean
     */
    public boolean isAlive(long currentTime) {
        if (!active.get()) {
            return false;
        }
        
        long elapsed = currentTime - lastFrameTime.get();
        return elapsed < timeoutSec * 1000L;
    }
    
    /**
     * 超时处理
     */
    public void onTimeout() {
        logger.debug("Session {} timed out", sessionId);
        onDetach(new RuntimeException("Session timeout"));
    }
    
    /**
     * 分离处理
     * 
     * @param ex 异常
     */
    public void onDetach(Exception ex) {
        if (!active.compareAndSet(true, false)) {
            return; // 已经分离
        }
        
        logger.debug("Session {} detached: {}", sessionId, ex.getMessage());
        
        // 清理资源
        if (gb28181Process != null) {
            gb28181Process.flush();
            gb28181Process = null;
        }
        
        if (rtpSplitter != null) {
            rtpSplitter.reset();
            rtpSplitter = null;
        }
        
        // 调用分离回调
        if (onDetachCallback != null) {
            try {
                onDetachCallback.accept(ex);
            } catch (Exception e) {
                logger.error("Error in detach callback for session {}", sessionId, e);
            }
        }
    }
    
    /**
     * 关闭会话
     */
    public void close() {
        onDetach(new RuntimeException("Session closed"));
    }
    
    // Getter和Setter方法
    
    public String getSessionId() {
        return sessionId;
    }
    
    public MediaTuple getMediaTuple() {
        return mediaTuple;
    }
    
    public InetSocketAddress getRemoteAddress() {
        return remoteAddress;
    }
    
    public boolean isUdp() {
        return isUdp;
    }
    
    public boolean isActive() {
        return active.get();
    }
    
    public long getTotalBytes() {
        return totalBytes.get();
    }
    
    public long getLastFrameTime() {
        return lastFrameTime.get();
    }
    
    public int getTimeoutSec() {
        return timeoutSec;
    }
    
    public void setTimeoutSec(int timeoutSec) {
        this.timeoutSec = timeoutSec;
    }
    
    public long getSsrc() {
        return ssrc;
    }
    
    public void setSsrc(long ssrc) {
        this.ssrc = ssrc;
    }
    
    public int getOnlyTrack() {
        return onlyTrack;
    }
    
    public void setOnlyTrack(int onlyTrack) {
        this.onlyTrack = onlyTrack;
    }
    
    public void setOnFrame(Consumer<Frame> callback) {
        this.onFrameCallback = callback;
    }
    
    public void setOnDetach(Consumer<Exception> callback) {
        this.onDetachCallback = callback;
    }

    /**
     * 获取媒体接收器
     *
     * @return 媒体接收器
     */
    public MediaSink getMediaSink() {
        return mediaSink;
    }

    /**
     * 获取媒体接收器统计信息
     *
     * @return 统计信息
     */
    public String getMediaStatistics() {
        return mediaSink != null ? mediaSink.getStatistics() : "No MediaSink";
    }
    
    @Override
    public String toString() {
        return "RtpSession{" +
               "sessionId='" + sessionId + '\'' +
               ", remoteAddress=" + remoteAddress +
               ", isUdp=" + isUdp +
               ", active=" + active.get() +
               ", totalBytes=" + totalBytes.get() +
               '}';
    }
}
