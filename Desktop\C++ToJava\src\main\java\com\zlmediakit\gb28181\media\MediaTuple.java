package com.zlmediakit.gb28181.media;

import java.util.Objects;

/**
 * 媒体元组
 * 基于ZLMediaKit的MediaTuple实现
 * 用于标识唯一的媒体流
 * 
 * <AUTHOR> Java Port
 */
public class MediaTuple {
    private String vhost = "__defaultVhost__";  // 虚拟主机
    private String app = "rtp";                 // 应用名
    private String stream;                      // 流ID
    private String params = "";                 // 参数
    
    /**
     * 默认构造函数
     */
    public MediaTuple() {
    }
    
    /**
     * 构造函数
     * 
     * @param vhost 虚拟主机
     * @param app 应用名
     * @param stream 流ID
     */
    public MediaTuple(String vhost, String app, String stream) {
        this.vhost = vhost;
        this.app = app;
        this.stream = stream;
    }
    
    /**
     * 构造函数
     * 
     * @param vhost 虚拟主机
     * @param app 应用名
     * @param stream 流ID
     * @param params 参数
     */
    public MediaTuple(String vhost, String app, String stream, String params) {
        this.vhost = vhost;
        this.app = app;
        this.stream = stream;
        this.params = params;
    }
    
    /**
     * 获取短URL
     * 
     * @return 短URL格式：app/stream
     */
    public String shortUrl() {
        return app + "/" + stream;
    }
    
    /**
     * 获取完整URL
     * 
     * @return 完整URL格式：vhost/app/stream
     */
    public String getUrl() {
        return vhost + "/" + app + "/" + stream;
    }
    
    /**
     * 获取标识符
     * 
     * @return 标识符
     */
    public String getIdentifier() {
        if (params != null && !params.isEmpty()) {
            return getUrl() + "?" + params;
        }
        return getUrl();
    }
    
    // Getter和Setter方法
    
    public String getVhost() {
        return vhost;
    }
    
    public void setVhost(String vhost) {
        this.vhost = vhost;
    }
    
    public String getApp() {
        return app;
    }
    
    public void setApp(String app) {
        this.app = app;
    }
    
    public String getStream() {
        return stream;
    }
    
    public void setStream(String stream) {
        this.stream = stream;
    }
    
    public String getParams() {
        return params;
    }
    
    public void setParams(String params) {
        this.params = params;
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        MediaTuple that = (MediaTuple) o;
        return Objects.equals(vhost, that.vhost) &&
               Objects.equals(app, that.app) &&
               Objects.equals(stream, that.stream) &&
               Objects.equals(params, that.params);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(vhost, app, stream, params);
    }
    
    @Override
    public String toString() {
        return "MediaTuple{" +
               "vhost='" + vhost + '\'' +
               ", app='" + app + '\'' +
               ", stream='" + stream + '\'' +
               ", params='" + params + '\'' +
               '}';
    }
}
