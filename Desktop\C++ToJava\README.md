# ZLMediaKit GB28181 Java移植项目文档

## 1. 项目概述

本项目是将ZLMediaKit中的GB28181视频流处理系统从C++移植到Java的完整实现。项目采用7层架构设计，实现了从网络接收到媒体输出的完整视频流处理流水线。

### 1.1 技术栈

- Java 11
- Netty 4.1.92 (网络框架)
- SLF4J + Logback (日志框架)
- <PERSON>ven (构建工具)
- JUnit 5 (测试框架)

### 1.2 核心功能

- GB28181协议支持
- RTP/RTCP协议处理
- H264/H265视频编解码
- TS/PS容器格式解析
- TCP/UDP双模式传输
- 会话管理和超时处理

## 2. 架构设计

### 2.1 7层架构

1. **网络接收层** - RtpServer、UdpRtpReceiver、TcpRtpReceiver
2. **会话管理层** - SessionManager、RtpSession、RtpSplitter
3. **RTP处理层** - RtpHeader、RtpPacket、RtpProcess
4. **GB28181协议层** - GB28181Process、RtpReceiver
5. **RTP解码层** - CommonRtpDecoder、H264RtpDecoder、H265RtpDecoder
6. **容器解码层** - TSDecoder、PSDecoder
7. **媒体输出层** - Frame、MediaSink、Track

### 2.2 数据流向

```
UDP/TCP数据 → RTP包解析 → 会话管理 → GB28181处理 → RTP解码 → 容器解码 → 媒体帧输出
```

## 3. 核心类说明

### 3.1 网络层

- **RtpServer**: 核心RTP服务器，管理UDP/TCP监听
- **UdpRtpReceiver**: UDP数据接收处理器
- **TcpRtpReceiver**: TCP数据接收处理器

### 3.2 会话层

- **SessionManager**: 会话管理器，负责会话创建和清理
- **RtpSession**: RTP会话，处理单个流的状态管理
- **RtpSplitter**: TCP RTP包分割器

### 3.3 RTP层

- **RtpHeader**: RTP头部解析
- **RtpPacket**: RTP包封装
- **RtpProcess**: RTP处理逻辑

### 3.4 协议层

- **GB28181Process**: GB28181协议处理核心
- **RtpReceiver**: RTP接收器接口

### 3.5 解码层

- **RtpDecoder**: RTP解码器接口
- **CommonRtpDecoder**: 通用RTP解码器
- **H264RtpDecoder**: H264专用RTP解码器
- **H265RtpDecoder**: H265专用RTP解码器
- **TSDecoder**: TS容器解码器
- **PSDecoder**: PS容器解码器

### 3.6 媒体层

- **Frame**: 媒体帧接口
- **FrameImpl**: 媒体帧实现
- **MediaTuple**: 媒体流标识
- **MediaSink**: 媒体接收器接口
- **MediaSinkImpl**: 媒体接收器实现
- **Track**: 媒体轨道接口
- **TrackImpl**: 媒体轨道实现

## 4. 关键特性

### 4.1 RTP解码器特性

- **CommonRtpDecoder**: 支持TS/PS等容器格式，自动检测编解码器类型
- **H264RtpDecoder**: 支持RFC 6184标准，处理STAP-A和FU-A包
- **H265RtpDecoder**: 支持RFC 7798标准，处理AP和FU包

### 4.2 容器解码器特性

- **TSDecoder**: 完整的MPEG-TS解析，支持PAT/PMT表解析和ES流提取
- **PSDecoder**: MPEG-PS解析，支持包头解析和PES流处理

### 4.3 媒体输出层特性

- **MediaSink**: 统一的媒体接收器，支持多轨道管理
- **Track**: 媒体轨道抽象，支持音视频轨道分离
- **自动轨道创建**: 根据帧类型自动创建对应轨道
- **统计信息**: 详细的帧率、比特率、丢帧统计
- **配置帧管理**: 自动识别和保存SPS/PPS等配置帧

### 4.4 网络特性

- 支持TCP/UDP双模式
- 自动会话管理和超时清理
- RTP包序列号检查和丢包处理
- TCP RTP包边界检测和重组

## 5. 配置说明

### 5.1 Maven配置

```xml
<properties>
    <maven.compiler.source>11</maven.compiler.source>
    <maven.compiler.target>11</maven.compiler.target>
    <netty.version>4.1.92.Final</netty.version>
</properties>
```

### 5.2 服务器配置

- 默认UDP端口: 10000
- 默认TCP端口: 10000
- 会话超时: 30秒
- 最大缓冲区: 2MB

## 6. 使用示例

### 6.1 启动服务器

```java
GB28181Server server = new GB28181Server();
server.start("0.0.0.0", 10000, GB28181Server.TcpMode.PASSIVE)
    .thenRun(() -> System.out.println("Server started"));
```

### 6.2 处理媒体帧

```java
rtpSession.setOnFrame(frame -> {
    System.out.println("Received frame: " + frame.getCodecName() +
                      ", size: " + frame.getSize());
});
```

### 6.3 获取媒体统计信息

```java
// 获取会话统计
String sessionStats = rtpSession.getStatistics();
System.out.println(sessionStats);

// 获取媒体接收器统计
String mediaStats = rtpSession.getMediaStatistics();
System.out.println(mediaStats);

// 获取特定轨道
MediaSink mediaSink = rtpSession.getMediaSink();
Track videoTrack = mediaSink.getTrack(Frame.TrackType.TrackVideo);
if (videoTrack != null) {
    System.out.println("Video track: " + videoTrack.getStatistics());
}
```

## 7. 当前进度

### 已完成任务

- [X] Task 1: 深入源码分析阶段 - 完成所有关键C++源文件分析
- [X] Task 2: Java项目架构设计 - 完成Maven项目结构和配置
- [X] Task 3: 网络接收层实现 - 完成RtpServer、UdpRtpReceiver、TcpRtpReceiver
- [X] Task 4: 会话管理层实现 - 完成SessionManager、RtpSession、RtpSplitter
- [X] Task 5: RTP处理层实现 - 完成RtpHeader、RtpPacket类
- [X] Task 6: GB28181协议层实现 - 完成GB28181Process、RtpReceiver
- [X] Task 7: RTP解码层实现 - 完成CommonRtpDecoder、H264RtpDecoder、H265RtpDecoder
- [X] Task 8: 容器解码层实现 - 完成TSDecoder、PSDecoder，集成到GB28181Process
- [X] Task 9: 媒体输出层实现 - 完成MediaSink、Track、输出管理，集成到RtpSession

### 当前任务

- [/] Task 10: 单元测试和文档 - 编写全面的单元测试和完善文档

### 待完成任务

- [ ] Task 10: 单元测试和文档 - 编写测试和文档
- [ ] Task 11: 代码检查和修复 - 使用diagnostics检查错误

## 8. 技术难点和解决方案

### 8.1 RTP包分片处理

- **问题**: H264/H265大帧需要分片传输
- **解决**: 实现FU-A/FU分片重组逻辑，支持序列号检查

### 8.2 TCP RTP包边界检测

- **问题**: TCP流没有包边界
- **解决**: 实现RtpSplitter进行包边界检测和重组

### 8.3 容器格式解析

- **问题**: TS/PS格式复杂，需要解析多层结构
- **解决**: 实现完整的表解析和流提取逻辑

### 8.4 时间戳转换

- **问题**: RTP时间戳(90kHz)需要转换为毫秒
- **解决**: 统一时间戳转换公式: ms = rtp_timestamp * 1000 / 90000

## 9. 性能优化

### 9.1 内存管理

- 使用ByteBuffer进行高效的字节操作
- 实现对象池减少GC压力
- 合理设置缓冲区大小

### 9.2 并发处理

- 使用ConcurrentHashMap管理会话
- 原子变量进行统计计数
- Netty EventLoop进行异步处理

## 10. 测试和验证

### 10.1 单元测试覆盖

- RTP包解析测试
- 解码器功能测试
- 会话管理测试
- 网络层测试

### 10.2 集成测试

- 端到端流处理测试
- 性能压力测试
- 异常情况处理测试

## 11. 当前进度

### 已完成任务

1. ✅ **深入源码分析阶段** - 完成对ZLMediaKit核心C++源码的深度分析
2. ✅ **Java项目架构设计** - 完成Maven项目结构和配置
3. ✅ **网络接收层实现** - 完成UDP/TCP RTP接收器
4. ✅ **会话管理层实现** - 完成会话管理和RTP分包
5. ✅ **RTP处理层实现** - 完成RTP包头和数据包处理
6. ✅ **GB28181协议层实现** - 完成协议处理和接收器
7. ✅ **RTP解码层实现** - 完成H264/H265 RTP解码器
8. ✅ **容器解码层实现** - 完成TS/PS容器解码器
9. ✅ **媒体输出层实现** - 完成媒体输出和轨道管理
10. ✅ **单元测试和文档** - 完成全面的单元测试，所有62个测试用例通过

### 测试结果

- **总测试数**: 62个
- **通过率**: 100%
- **测试覆盖**:
  - RtpHeader: 13个测试
  - RtpPacket: 15个测试
  - H264RtpDecoder: 10个测试
  - TSDecoder: 12个测试
  - MediaSink: 12个测试

### 已完成任务（续）

11. ✅ **代码检查和修复** - 完成代码质量检查和优化

### 项目完成情况

- ✅ 所有11个任务全部完成
- ✅ 62个单元测试100%通过
- ✅ Maven构建成功，生成可执行JAR包
- ✅ 代码质量检查完成，修复了主要问题
- ✅ 项目文档完整

---

**项目状态**: ✅ 已完成
**最后更新**: 2025-08-01
**版本**: 1.0.0-SNAPSHOT
**构建产物**: gb28181-java-1.0.0.jar
