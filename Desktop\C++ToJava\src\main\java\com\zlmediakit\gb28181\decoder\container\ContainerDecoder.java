package com.zlmediakit.gb28181.decoder.container;

import com.zlmediakit.gb28181.media.Frame;

import java.util.function.Consumer;

/**
 * 容器解码器接口
 * 基于ZLMediaKit的容器解码器设计
 * 
 * <AUTHOR> Java Port
 */
public interface ContainerDecoder {
    
    /**
     * 输入数据
     * 
     * @param data 输入数据
     * @return 是否处理成功
     */
    boolean input(byte[] data);
    
    /**
     * 输入数据
     * 
     * @param data 输入数据
     * @param offset 偏移量
     * @param length 长度
     * @return 是否处理成功
     */
    boolean input(byte[] data, int offset, int length);
    
    /**
     * 设置帧回调
     * 
     * @param callback 帧回调函数
     */
    void setOnFrame(Consumer<Frame> callback);
    
    /**
     * 刷新缓存
     */
    void flush();
    
    /**
     * 获取解码器名称
     * 
     * @return 解码器名称
     */
    String getDecoderName();
    
    /**
     * 获取统计信息
     * 
     * @return 统计信息
     */
    String getStatistics();
    
    /**
     * 重置解码器状态
     */
    void reset();
    
    /**
     * 检查解码器是否就绪
     * 
     * @return boolean
     */
    boolean isReady();
}
