package com.zlmediakit.gb28181.decoder.rtp;

import com.zlmediakit.gb28181.media.Frame;
import com.zlmediakit.gb28181.media.FrameImpl;
import com.zlmediakit.gb28181.rtp.RtpPacket;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.nio.ByteBuffer;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Consumer;

/**
 * 通用RTP解码器
 * 基于ZLMediaKit的CommonRtpDecoder实现
 * 用于处理TS/PS等容器格式的RTP流
 * 
 * <AUTHOR> Java Port
 */
public class CommonRtpDecoder implements RtpDecoder {
    private static final Logger logger = LoggerFactory.getLogger(CommonRtpDecoder.class);
    
    // 缓冲区
    private final ByteBuffer buffer;
    private final int maxBufferSize;
    
    // 回调函数
    private Consumer<Frame> onFrameCallback;
    
    // 状态变量
    private long lastTimestamp = -1;
    private boolean ready = false;
    
    // 统计信息
    private final AtomicLong totalPackets = new AtomicLong(0);
    private final AtomicLong totalBytes = new AtomicLong(0);
    private final AtomicLong outputFrames = new AtomicLong(0);
    
    /**
     * 构造函数
     * 
     * @param maxBufferSize 最大缓冲区大小
     */
    public CommonRtpDecoder(int maxBufferSize) {
        this.maxBufferSize = maxBufferSize;
        this.buffer = ByteBuffer.allocate(maxBufferSize);
        this.ready = true;
        
        logger.debug("Created CommonRtpDecoder with buffer size: {}", maxBufferSize);
    }
    
    /**
     * 默认构造函数
     */
    public CommonRtpDecoder() {
        this(64 * 1024); // 64KB默认缓冲区
    }
    
    @Override
    public boolean inputRtp(RtpPacket packet) {
        if (packet == null || !ready) {
            return false;
        }
        
        try {
            totalPackets.incrementAndGet();
            
            byte[] payload = packet.getPayload();
            if (payload == null || payload.length == 0) {
                return true; // 空负载，跳过
            }
            
            totalBytes.addAndGet(payload.length);
            
            // 检查时间戳变化
            long timestamp = packet.getTimestamp();
            boolean timestampChanged = (lastTimestamp != -1 && lastTimestamp != timestamp);
            
            if (timestampChanged) {
                // 时间戳变化，输出之前的帧
                flushFrame(lastTimestamp);
            }
            
            // 添加数据到缓冲区
            if (buffer.remaining() < payload.length) {
                // 缓冲区空间不足
                if (buffer.position() > 0) {
                    // 输出当前帧
                    flushFrame(lastTimestamp);
                }
                
                // 如果单个包就超过缓冲区大小，直接输出
                if (payload.length > maxBufferSize) {
                    outputFrame(payload, timestamp);
                    lastTimestamp = timestamp;
                    return true;
                }
            }
            
            // 添加到缓冲区
            buffer.put(payload);
            lastTimestamp = timestamp;
            
            // 如果是标记包，立即输出
            if (packet.isMarker()) {
                flushFrame(timestamp);
            }
            
            return true;
            
        } catch (Exception e) {
            logger.error("Error processing RTP packet in CommonRtpDecoder", e);
            return false;
        }
    }
    
    /**
     * 刷新当前帧
     * 
     * @param timestamp 时间戳
     */
    private void flushFrame(long timestamp) {
        if (buffer.position() == 0) {
            return; // 没有数据
        }
        
        // 创建帧数据
        byte[] frameData = new byte[buffer.position()];
        buffer.flip();
        buffer.get(frameData);
        buffer.clear();
        
        // 输出帧
        outputFrame(frameData, timestamp);
    }
    
    /**
     * 输出帧
     * 
     * @param data 帧数据
     * @param timestamp RTP时间戳
     */
    private void outputFrame(byte[] data, long timestamp) {
        if (onFrameCallback == null || data == null || data.length == 0) {
            return;
        }
        
        try {
            // 转换时间戳（假设90kHz时钟）
            long dts = timestamp * 1000 / 90000;
            long pts = dts;
            
            // 创建帧
            FrameImpl frame = new FrameImpl();
            frame.setData(data);
            frame.setDts(dts);
            frame.setPts(pts);
            frame.setCodecId(detectCodecId(data));
            
            // 调用回调
            onFrameCallback.accept(frame);
            outputFrames.incrementAndGet();
            
            logger.debug("Output frame: size={}, timestamp={}, dts={}", 
                        data.length, timestamp, dts);
            
        } catch (Exception e) {
            logger.error("Error outputting frame", e);
        }
    }
    
    /**
     * 检测编解码器ID
     * 
     * @param data 数据
     * @return 编解码器ID
     */
    private Frame.CodecId detectCodecId(byte[] data) {
        if (data == null || data.length < 4) {
            return Frame.CodecId.CodecInvalid;
        }
        
        // 检测TS格式
        if (data.length % 188 == 0 && data[0] == 0x47) {
            return Frame.CodecId.CodecTS;
        }
        
        // 检测PS格式
        if (data.length >= 4 && 
            data[0] == 0x00 && data[1] == 0x00 && 
            data[2] == 0x01 && (data[3] & 0xFF) == 0xBA) {
            return Frame.CodecId.CodecPS;
        }
        
        // 检测H264格式
        if (isH264Data(data)) {
            return Frame.CodecId.CodecH264;
        }
        
        // 检测H265格式
        if (isH265Data(data)) {
            return Frame.CodecId.CodecH265;
        }
        
        // 默认返回无效
        return Frame.CodecId.CodecInvalid;
    }
    
    /**
     * 检测是否为H264数据
     * 
     * @param data 数据
     * @return boolean
     */
    private boolean isH264Data(byte[] data) {
        if (data.length < 4) {
            return false;
        }
        
        // 查找H264起始码
        for (int i = 0; i <= data.length - 4; i++) {
            if (data[i] == 0x00 && data[i + 1] == 0x00 && 
                data[i + 2] == 0x00 && data[i + 3] == 0x01) {
                return true;
            } else if (i <= data.length - 3 && 
                      data[i] == 0x00 && data[i + 1] == 0x00 && data[i + 2] == 0x01) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 检测是否为H265数据
     * 
     * @param data 数据
     * @return boolean
     */
    private boolean isH265Data(byte[] data) {
        if (data.length < 5) {
            return false;
        }
        
        // 查找H265起始码和NALU类型
        for (int i = 0; i <= data.length - 5; i++) {
            if (data[i] == 0x00 && data[i + 1] == 0x00 && 
                data[i + 2] == 0x00 && data[i + 3] == 0x01) {
                int naluType = (data[i + 4] & 0x7E) >> 1;
                if (naluType >= 0 && naluType <= 40) { // H265 NALU类型范围
                    return true;
                }
            }
        }
        
        return false;
    }
    
    @Override
    public void setOnFrame(Consumer<Frame> callback) {
        this.onFrameCallback = callback;
    }
    
    @Override
    public void flush() {
        if (lastTimestamp != -1) {
            flushFrame(lastTimestamp);
        }
        reset();
    }
    
    @Override
    public Frame.CodecId getCodecId() {
        return Frame.CodecId.CodecInvalid; // 通用解码器不确定编解码器类型
    }
    
    @Override
    public String getDecoderName() {
        return "CommonRtpDecoder";
    }
    
    @Override
    public String getStatistics() {
        StringBuilder sb = new StringBuilder();
        sb.append("CommonRtpDecoder Statistics:\n");
        sb.append("  Total Packets: ").append(totalPackets.get()).append("\n");
        sb.append("  Total Bytes: ").append(totalBytes.get()).append("\n");
        sb.append("  Output Frames: ").append(outputFrames.get()).append("\n");
        sb.append("  Buffer Usage: ").append(buffer.position()).append("/").append(maxBufferSize).append("\n");
        sb.append("  Ready: ").append(ready).append("\n");
        sb.append("  Last Timestamp: ").append(lastTimestamp).append("\n");
        
        return sb.toString();
    }
    
    @Override
    public void reset() {
        buffer.clear();
        lastTimestamp = -1;
        ready = true;
        
        logger.debug("CommonRtpDecoder reset");
    }
    
    @Override
    public boolean isReady() {
        return ready;
    }
}
