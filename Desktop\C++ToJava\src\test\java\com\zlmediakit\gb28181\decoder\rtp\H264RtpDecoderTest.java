package com.zlmediakit.gb28181.decoder.rtp;

import com.zlmediakit.gb28181.media.Frame;
import com.zlmediakit.gb28181.rtp.RtpPacket;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import static org.junit.jupiter.api.Assertions.*;

import java.util.ArrayList;
import java.util.List;

/**
 * H264RtpDecoder单元测试
 * 
 * <AUTHOR> Java Port
 */
public class H264RtpDecoderTest {
    
    private H264RtpDecoder decoder;
    private List<Frame> receivedFrames;
    
    @BeforeEach
    void setUp() {
        decoder = new H264RtpDecoder();
        receivedFrames = new ArrayList<>();
        decoder.setOnFrame(receivedFrames::add);
    }
    
    @Test
    void testDecoderBasicProperties() {
        assertEquals(Frame.CodecId.CodecH264, decoder.getCodecId());
        assertEquals("H264RtpDecoder", decoder.getDecoderName());
        assertTrue(decoder.isReady()); // H264RtpDecoder默认就绪状态
    }
    
    @Test
    void testSingleNaluPacket() {
        // 创建单个NALU包 (类型1-23)
        byte[] naluData = {
            0x67, 0x42, 0x00, 0x1E, // SPS开始
            (byte) 0x8D, (byte) 0x8B, 0x40, 0x50
        };

        RtpPacket packet = createRtpPacket(naluData, 1, 1000, false);

        assertTrue(decoder.inputRtp(packet));
        decoder.flush(); // 需要flush才能输出帧
        assertEquals(1, receivedFrames.size());
        
        Frame frame = receivedFrames.get(0);
        assertEquals(Frame.CodecId.CodecH264, frame.getCodecId());
        assertTrue(frame.isConfigFrame()); // SPS是配置帧
        
        // 检查帧数据包含起始码
        byte[] frameData = frame.getData();
        assertTrue(frameData.length >= 4);
        assertEquals(0x00, frameData[0]);
        assertEquals(0x00, frameData[1]);
        assertEquals(0x00, frameData[2]);
        assertEquals(0x01, frameData[3]);
    }
    
    @Test
    void testStapAPacket() {
        // 创建STAP-A包 (类型24)
        byte[] stapData = {
            0x18, // STAP-A类型 (24)
            0x00, 0x04, // 第一个NALU长度
            0x67, 0x42, 0x00, 0x1E, // SPS NALU
            0x00, 0x04, // 第二个NALU长度  
            0x68, (byte) 0xCE, 0x3C, (byte) 0x80 // PPS NALU
        };
        
        RtpPacket packet = createRtpPacket(stapData, 1, 1000, false);

        assertTrue(decoder.inputRtp(packet));
        decoder.flush(); // 需要flush才能输出帧
        assertEquals(1, receivedFrames.size()); // STAP-A应该产生一个帧
        
        Frame frame = receivedFrames.get(0);
        byte[] frameData = frame.getData();

        // 应该包含两个NALU，每个都有起始码
        assertTrue(frameData.length > 8); // 至少包含两个起始码和NALU数据
    }
    
    @Test
    void testFuAFragmentation() {
        // 创建FU-A分片包序列
        byte[] originalNalu = new byte[100];
        originalNalu[0] = 0x65; // IDR帧
        for (int i = 1; i < originalNalu.length; i++) {
            originalNalu[i] = (byte) (i % 256);
        }
        
        // 分成3个FU-A包
        List<RtpPacket> fuPackets = createFuAPackets(originalNalu, 1000);
        
        // 输入所有分片
        for (RtpPacket packet : fuPackets) {
            assertTrue(decoder.inputRtp(packet));
        }
        
        assertEquals(1, receivedFrames.size());
        
        Frame frame = receivedFrames.get(0);
        assertTrue(frame.isKeyFrame()); // IDR帧应该是关键帧
        
        byte[] frameData = frame.getData();
        // 检查起始码
        assertEquals(0x00, frameData[0]);
        assertEquals(0x00, frameData[1]);
        assertEquals(0x00, frameData[2]);
        assertEquals(0x01, frameData[3]);
        // 检查NALU类型
        assertEquals(0x65, frameData[4]);
    }
    
    @Test
    void testOutOfOrderPackets() {
        // 测试乱序包处理 - 简化测试，只验证不会崩溃
        byte[] originalNalu = new byte[60];
        originalNalu[0] = 0x61; // P帧
        for (int i = 1; i < originalNalu.length; i++) {
            originalNalu[i] = (byte) i;
        }

        List<RtpPacket> fuPackets = createFuAPackets(originalNalu, 2000);

        // 乱序输入：2, 1, 3 - 可能不会产生完整帧
        decoder.inputRtp(fuPackets.get(1)); // 中间包
        decoder.inputRtp(fuPackets.get(0)); // 开始包
        decoder.inputRtp(fuPackets.get(2)); // 结束包
        decoder.flush();

        // 乱序包可能导致帧丢失，这是正常的
        assertTrue(receivedFrames.size() >= 0);
    }
    
    @Test
    void testPacketLoss() {
        // 创建FU-A分片包，模拟丢包
        byte[] originalNalu = new byte[80];
        originalNalu[0] = 0x61;

        List<RtpPacket> fuPackets = createFuAPackets(originalNalu, 3000);

        // 只输入第一个和最后一个包（中间包丢失）
        decoder.inputRtp(fuPackets.get(0));
        decoder.inputRtp(fuPackets.get(2));
        decoder.flush();

        // 丢包情况下可能没有完整帧输出，这是正常的
        assertTrue(receivedFrames.size() >= 0);
    }
    
    @Test
    void testSequenceNumberWrap() {
        // 测试序列号回绕
        byte[] naluData = {0x61, 0x01, 0x02, 0x03};

        // 序列号接近最大值，使用相同时间戳
        RtpPacket packet1 = createRtpPacket(naluData, 65534, 4000, false);
        RtpPacket packet2 = createRtpPacket(naluData, 65535, 4100, false);
        RtpPacket packet3 = createRtpPacket(naluData, 0, 4200, false); // 回绕

        assertTrue(decoder.inputRtp(packet1));
        assertTrue(decoder.inputRtp(packet2)); // 时间戳变化，输出第一帧
        assertTrue(decoder.inputRtp(packet3)); // 时间戳变化，输出第二帧
        decoder.flush(); // 输出最后一帧

        assertEquals(3, receivedFrames.size());
    }
    
    @Test
    void testFlush() {
        // 输入不完整的FU-A序列
        byte[] originalNalu = new byte[50];
        originalNalu[0] = 0x61;
        
        List<RtpPacket> fuPackets = createFuAPackets(originalNalu, 5000);
        
        // 只输入第一个包
        assertTrue(decoder.inputRtp(fuPackets.get(0)));
        assertEquals(0, receivedFrames.size());
        
        // 刷新应该清空缓存
        decoder.flush();
        
        // 输入新的完整包
        byte[] newNalu = {0x67, 0x42, 0x00, 0x1E};
        RtpPacket newPacket = createRtpPacket(newNalu, 1, 6000, false);
        assertTrue(decoder.inputRtp(newPacket));
        
        assertEquals(1, receivedFrames.size());
    }
    
    @Test
    void testReset() {
        // 输入一些包
        byte[] naluData = {0x67, 0x42, 0x00, 0x1E};
        RtpPacket packet = createRtpPacket(naluData, 1, 1000, false);
        assertTrue(decoder.inputRtp(packet));
        decoder.flush();

        assertEquals(1, receivedFrames.size());

        // 重置
        decoder.reset();
        receivedFrames.clear();

        // 重置后应该能正常工作
        assertTrue(decoder.inputRtp(packet));
        decoder.flush();
        assertEquals(1, receivedFrames.size());
    }
    
    @Test
    void testStatistics() {
        String stats = decoder.getStatistics();
        assertNotNull(stats);
        assertTrue(stats.contains("H264RtpDecoder"));
    }
    
    /**
     * 创建RTP包
     */
    private RtpPacket createRtpPacket(byte[] payload, int seq, long timestamp, boolean marker) {
        byte[] rtpData = new byte[12 + payload.length];
        
        // RTP头部
        rtpData[0] = (byte) 0x80; // V=2
        rtpData[1] = (byte) (marker ? 0xE0 : 0x60); // M位和PT=96
        rtpData[2] = (byte) ((seq >> 8) & 0xFF);
        rtpData[3] = (byte) (seq & 0xFF);
        rtpData[4] = (byte) ((timestamp >> 24) & 0xFF);
        rtpData[5] = (byte) ((timestamp >> 16) & 0xFF);
        rtpData[6] = (byte) ((timestamp >> 8) & 0xFF);
        rtpData[7] = (byte) (timestamp & 0xFF);
        rtpData[8] = 0x12; // SSRC
        rtpData[9] = 0x34;
        rtpData[10] = 0x56;
        rtpData[11] = 0x78;
        
        // 负载
        System.arraycopy(payload, 0, rtpData, 12, payload.length);
        
        return new RtpPacket(rtpData, 0, rtpData.length);
    }
    
    /**
     * 创建FU-A分片包
     */
    private List<RtpPacket> createFuAPackets(byte[] nalu, long baseTimestamp) {
        List<RtpPacket> packets = new ArrayList<>();
        
        byte naluType = (byte) (nalu[0] & 0x1F);
        byte naluNri = (byte) (nalu[0] & 0x60);
        
        // 分成3个包
        int chunkSize = (nalu.length - 1) / 3;
        int seq = 1;
        
        for (int i = 0; i < 3; i++) {
            boolean isFirst = (i == 0);
            boolean isLast = (i == 2);
            
            int start = i * chunkSize + 1; // 跳过原始NALU头
            int end = isLast ? nalu.length : (i + 1) * chunkSize + 1;
            int payloadSize = end - start;
            
            byte[] fuPayload = new byte[2 + payloadSize];
            fuPayload[0] = (byte) (naluNri | 28); // FU-A类型
            fuPayload[1] = (byte) ((isFirst ? 0x80 : 0) | (isLast ? 0x40 : 0) | naluType);
            
            System.arraycopy(nalu, start, fuPayload, 2, payloadSize);
            
            RtpPacket packet = createRtpPacket(fuPayload, seq++, baseTimestamp, isLast);
            packets.add(packet);
        }
        
        return packets;
    }
}
