package com.zlmediakit.gb28181.rtp;

import java.nio.ByteBuffer;

/**
 * RTP头部解析类
 * 基于ZLMediaKit的RTP头部处理逻辑
 * 
 * <AUTHOR> Java Port
 */
public class RtpHeader {
    // RTP头部字段
    private int version;        // 版本号 (2 bits)
    private boolean padding;    // 填充位 (1 bit)
    private boolean extension;  // 扩展位 (1 bit)
    private int csrcCount;      // CSRC计数 (4 bits)
    private boolean marker;     // 标记位 (1 bit)
    private int payloadType;    // 负载类型 (7 bits)
    private int sequenceNumber; // 序列号 (16 bits)
    private long timestamp;     // 时间戳 (32 bits)
    private long ssrc;          // 同步源标识符 (32 bits)
    private long[] csrcList;    // CSRC列表
    
    // 扩展头部
    private int extensionProfile;
    private byte[] extensionData;
    
    // 原始数据
    private byte[] rawData;
    private int headerLength;
    
    /**
     * 构造函数
     * 
     * @param data RTP包数据
     * @param offset 偏移量
     */
    public RtpHeader(byte[] data, int offset) {
        parseHeader(data, offset);
    }
    
    /**
     * 构造函数
     * 
     * @param data RTP包数据
     */
    public RtpHeader(byte[] data) {
        this(data, 0);
    }
    
    /**
     * 解析RTP头部
     * 
     * @param data 数据
     * @param offset 偏移量
     */
    private void parseHeader(byte[] data, int offset) {
        if (data == null || data.length < offset + 12) {
            throw new IllegalArgumentException("Invalid RTP packet: too short");
        }
        
        this.rawData = data;
        ByteBuffer buffer = ByteBuffer.wrap(data, offset, data.length - offset);
        
        // 解析第一个字节
        byte firstByte = buffer.get();
        this.version = (firstByte & 0xC0) >> 6;
        this.padding = (firstByte & 0x20) != 0;
        this.extension = (firstByte & 0x10) != 0;
        this.csrcCount = firstByte & 0x0F;
        
        // 验证版本号
        if (version != 2) {
            throw new IllegalArgumentException("Invalid RTP version: " + version);
        }
        
        // 解析第二个字节
        byte secondByte = buffer.get();
        this.marker = (secondByte & 0x80) != 0;
        this.payloadType = secondByte & 0x7F;
        
        // 解析序列号
        this.sequenceNumber = buffer.getShort() & 0xFFFF;
        
        // 解析时间戳
        this.timestamp = buffer.getInt() & 0xFFFFFFFFL;
        
        // 解析SSRC
        this.ssrc = buffer.getInt() & 0xFFFFFFFFL;
        
        // 解析CSRC列表
        if (csrcCount > 0) {
            csrcList = new long[csrcCount];
            for (int i = 0; i < csrcCount; i++) {
                csrcList[i] = buffer.getInt() & 0xFFFFFFFFL;
            }
        }
        
        // 计算基本头部长度
        headerLength = 12 + csrcCount * 4;
        
        // 解析扩展头部
        if (extension) {
            if (buffer.remaining() < 4) {
                throw new IllegalArgumentException("Invalid RTP extension header");
            }
            
            extensionProfile = buffer.getShort() & 0xFFFF;
            int extensionLength = buffer.getShort() & 0xFFFF;
            
            if (buffer.remaining() < extensionLength * 4) {
                throw new IllegalArgumentException("Invalid RTP extension data");
            }
            
            extensionData = new byte[extensionLength * 4];
            buffer.get(extensionData);
            
            headerLength += 4 + extensionData.length;
        }
    }
    
    /**
     * 获取负载数据
     * 
     * @return 负载数据
     */
    public byte[] getPayload() {
        if (rawData == null) {
            return new byte[0];
        }
        
        int payloadOffset = headerLength;
        int payloadLength = rawData.length - payloadOffset;
        
        // 处理填充
        if (padding && payloadLength > 0) {
            int paddingLength = rawData[rawData.length - 1] & 0xFF;
            payloadLength -= paddingLength;
        }
        
        if (payloadLength <= 0) {
            return new byte[0];
        }
        
        byte[] payload = new byte[payloadLength];
        System.arraycopy(rawData, payloadOffset, payload, 0, payloadLength);
        return payload;
    }
    
    /**
     * 获取负载长度
     * 
     * @return 负载长度
     */
    public int getPayloadLength() {
        if (rawData == null) {
            return 0;
        }
        
        int payloadLength = rawData.length - headerLength;
        
        // 处理填充
        if (padding && payloadLength > 0) {
            int paddingLength = rawData[rawData.length - 1] & 0xFF;
            payloadLength -= paddingLength;
        }
        
        return Math.max(0, payloadLength);
    }
    
    /**
     * 检查是否为有效的RTP包
     * 
     * @param data 数据
     * @param offset 偏移量
     * @param length 长度
     * @return boolean
     */
    public static boolean isValidRtpPacket(byte[] data, int offset, int length) {
        if (data == null || length < 12) {
            return false;
        }
        
        try {
            // 检查版本号
            int version = (data[offset] & 0xC0) >> 6;
            if (version != 2) {
                return false;
            }
            
            // 检查CSRC计数
            int csrcCount = data[offset] & 0x0F;
            int minLength = 12 + csrcCount * 4;
            
            if (length < minLength) {
                return false;
            }
            
            // 检查扩展头
            boolean extension = (data[offset] & 0x10) != 0;
            if (extension) {
                if (length < minLength + 4) {
                    return false;
                }
                
                // 读取扩展长度
                ByteBuffer buffer = ByteBuffer.wrap(data, offset + minLength + 2, 2);
                int extLength = buffer.getShort() & 0xFFFF;
                minLength += 4 + extLength * 4;
                
                if (length < minLength) {
                    return false;
                }
            }
            
            return true;
            
        } catch (Exception e) {
            return false;
        }
    }
    
    // Getter方法
    
    public int getVersion() {
        return version;
    }
    
    public boolean isPadding() {
        return padding;
    }
    
    public boolean isExtension() {
        return extension;
    }
    
    public int getCsrcCount() {
        return csrcCount;
    }
    
    public boolean isMarker() {
        return marker;
    }
    
    public int getPayloadType() {
        return payloadType;
    }
    
    public int getSequenceNumber() {
        return sequenceNumber;
    }
    
    public long getTimestamp() {
        return timestamp;
    }
    
    public long getSsrc() {
        return ssrc;
    }
    
    public long[] getCsrcList() {
        return csrcList;
    }
    
    public int getExtensionProfile() {
        return extensionProfile;
    }
    
    public byte[] getExtensionData() {
        return extensionData;
    }
    
    public int getHeaderLength() {
        return headerLength;
    }
    
    @Override
    public String toString() {
        return "RtpHeader{" +
               "version=" + version +
               ", padding=" + padding +
               ", extension=" + extension +
               ", csrcCount=" + csrcCount +
               ", marker=" + marker +
               ", payloadType=" + payloadType +
               ", sequenceNumber=" + sequenceNumber +
               ", timestamp=" + timestamp +
               ", ssrc=" + ssrc +
               ", headerLength=" + headerLength +
               ", payloadLength=" + getPayloadLength() +
               '}';
    }
}
