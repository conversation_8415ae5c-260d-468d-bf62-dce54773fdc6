package com.zlmediakit.gb28181.network;

import com.zlmediakit.gb28181.session.RtpSession;
import io.netty.buffer.ByteBuf;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.SimpleChannelInboundHandler;
import io.netty.channel.socket.DatagramPacket;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.InetSocketAddress;

/**
 * UDP RTP数据接收器
 * 基于ZLMediaKit的UDP RTP处理逻辑
 * 
 * <AUTHOR> Java Port
 */
public class UdpRtpReceiver extends SimpleChannelInboundHandler<DatagramPacket> {
    private static final Logger logger = LoggerFactory.getLogger(UdpRtpReceiver.class);
    
    private final RtpServer rtpServer;
    
    /**
     * 构造函数
     * 
     * @param rtpServer RTP服务器实例
     */
    public UdpRtpReceiver(RtpServer rtpServer) {
        this.rtpServer = rtpServer;
    }
    
    @Override
    protected void channelRead0(ChannelHandlerContext ctx, DatagramPacket packet) throws Exception {
        try {
            InetSocketAddress remoteAddress = packet.sender();
            ByteBuf content = packet.content();
            
            if (content.readableBytes() < 12) {
                // RTP包最小长度为12字节
                logger.debug("Received packet too small: {} bytes from {}", 
                           content.readableBytes(), remoteAddress);
                return;
            }
            
            // 检查是否为RTP包
            if (!isRtpPacket(content)) {
                logger.debug("Received non-RTP packet from {}", remoteAddress);
                return;
            }
            
            // 获取或创建会话
            RtpSession session = rtpServer.getSession(remoteAddress, true);
            if (session == null) {
                session = rtpServer.createSession(remoteAddress, true);
            }
            
            // 复制数据到字节数组
            byte[] data = new byte[content.readableBytes()];
            content.readBytes(data);
            
            // 处理RTP包
            session.inputRtp(data, remoteAddress);
            
        } catch (Exception e) {
            logger.error("Error processing UDP RTP packet", e);
        }
    }
    
    /**
     * 检查是否为RTP包
     * 
     * @param buffer 数据缓冲区
     * @return boolean
     */
    private boolean isRtpPacket(ByteBuf buffer) {
        if (buffer.readableBytes() < 12) {
            return false;
        }
        
        // 保存读取位置
        int readerIndex = buffer.readerIndex();
        
        try {
            // 读取RTP头部第一个字节
            byte firstByte = buffer.getByte(readerIndex);
            
            // 检查版本号（前2位应该是2）
            int version = (firstByte & 0xC0) >> 6;
            if (version != 2) {
                return false;
            }
            
            // 检查负载类型
            byte payloadType = buffer.getByte(readerIndex + 1);
            int pt = payloadType & 0x7F;
            
            // 常见的RTP负载类型范围检查
            return pt >= 0 && pt <= 127;
            
        } catch (Exception e) {
            return false;
        }
    }
    
    @Override
    public void channelActive(ChannelHandlerContext ctx) throws Exception {
        logger.info("UDP RTP receiver channel active: {}", ctx.channel().localAddress());
        super.channelActive(ctx);
    }
    
    @Override
    public void channelInactive(ChannelHandlerContext ctx) throws Exception {
        logger.info("UDP RTP receiver channel inactive: {}", ctx.channel().localAddress());
        super.channelInactive(ctx);
    }
    
    @Override
    public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) throws Exception {
        logger.error("UDP RTP receiver exception", cause);
        // 不关闭UDP通道，继续处理其他数据包
    }
}
