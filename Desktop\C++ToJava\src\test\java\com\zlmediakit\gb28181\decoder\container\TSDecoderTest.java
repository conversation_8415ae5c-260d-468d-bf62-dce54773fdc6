package com.zlmediakit.gb28181.decoder.container;

import com.zlmediakit.gb28181.media.Frame;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import static org.junit.jupiter.api.Assertions.*;

import java.util.ArrayList;
import java.util.List;

/**
 * TSDecoder单元测试
 * 
 * <AUTHOR> Java Port
 */
public class TSDecoderTest {
    
    private TSDecoder decoder;
    private List<Frame> receivedFrames;
    
    @BeforeEach
    void setUp() {
        decoder = new TSDecoder();
        receivedFrames = new ArrayList<>();
        decoder.setOnFrame(receivedFrames::add);
    }
    
    @Test
    void testDecoderBasicProperties() {
        assertEquals("TSDecoder", decoder.getDecoderName());
        assertTrue(decoder.isReady()); // TSDecoder默认ready=true
    }
    
    @Test
    void testInputValidTSPacket() {
        // 创建一个基本的TS包 (188字节)
        byte[] tsPacket = createBasicTSPacket();
        
        assertTrue(decoder.input(tsPacket, 0, tsPacket.length));

        // 基本TS包解析不会立即产生帧，需要完整的PES包
        // 这里主要测试不会抛出异常
    }

    @Test
    void testInputInvalidData() {
        // 空数据测试 - 不会抛出异常即可
        try {
            decoder.input(null, 0, 0);
        } catch (Exception e) {
            // 可能抛出异常，这是正常的
        }

        // 太短的数据
        byte[] shortData = new byte[100];
        decoder.input(shortData, 0, shortData.length);

        // 无效的同步字节
        byte[] invalidSync = new byte[188];
        invalidSync[0] = 0x48; // 错误的同步字节
        decoder.input(invalidSync, 0, invalidSync.length);

        // 基本验证：测试不会抛出异常
        assertTrue(true);
    }
    
    @Test
    void testMultipleTSPackets() {
        // 创建多个TS包
        byte[] packet1 = createBasicTSPacket();
        byte[] packet2 = createBasicTSPacket();
        byte[] packet3 = createBasicTSPacket();
        
        assertTrue(decoder.input(packet1, 0, packet1.length));
        assertTrue(decoder.input(packet2, 0, packet2.length));
        assertTrue(decoder.input(packet3, 0, packet3.length));
        
        // 验证解码器状态
        assertNotNull(decoder.getStatistics());
    }
    
    @Test
    void testFlush() {
        // 输入一些数据
        byte[] tsPacket = createBasicTSPacket();
        decoder.input(tsPacket, 0, tsPacket.length);

        // 刷新
        decoder.flush();

        // 刷新后应该能继续正常工作
        assertTrue(decoder.input(tsPacket, 0, tsPacket.length));
    }
    
    @Test
    void testReset() {
        // 输入一些数据
        byte[] tsPacket = createBasicTSPacket();
        decoder.input(tsPacket, 0, tsPacket.length);

        // 重置
        decoder.reset();

        // 重置后应该能正常工作
        assertTrue(decoder.input(tsPacket, 0, tsPacket.length));
    }
    
    @Test
    void testStatistics() {
        String stats = decoder.getStatistics();
        assertNotNull(stats);
        assertTrue(stats.contains("TSDecoder"));
    }
    
    @Test
    void testPATPacket() {
        // 创建PAT包 (PID=0)
        byte[] patPacket = createPATPacket();
        
        assertTrue(decoder.input(patPacket, 0, patPacket.length));

        // PAT包不会产生媒体帧
        assertEquals(0, receivedFrames.size());
    }

    @Test
    void testPMTPacket() {
        // 先输入PAT包
        byte[] patPacket = createPATPacket();
        decoder.input(patPacket, 0, patPacket.length);

        // 再输入PMT包
        byte[] pmtPacket = createPMTPacket();
        assertTrue(decoder.input(pmtPacket, 0, pmtPacket.length));
        
        // PMT包不会产生媒体帧
        assertEquals(0, receivedFrames.size());
    }
    
    @Test
    void testContinuityCounter() {
        // 创建连续的TS包
        byte[] packet1 = createTSPacketWithCC(0x100, 0); // PID=0x100, CC=0
        byte[] packet2 = createTSPacketWithCC(0x100, 1); // PID=0x100, CC=1
        byte[] packet3 = createTSPacketWithCC(0x100, 2); // PID=0x100, CC=2
        
        assertTrue(decoder.input(packet1, 0, packet1.length));
        assertTrue(decoder.input(packet2, 0, packet2.length));
        assertTrue(decoder.input(packet3, 0, packet3.length));
    }
    
    @Test
    void testDiscontinuity() {
        // 创建不连续的TS包
        byte[] packet1 = createTSPacketWithCC(0x100, 0); // PID=0x100, CC=0
        byte[] packet2 = createTSPacketWithCC(0x100, 5); // PID=0x100, CC=5 (跳跃)
        
        assertTrue(decoder.input(packet1, 0, packet1.length));
        assertTrue(decoder.input(packet2, 0, packet2.length));

        // 解码器应该能处理不连续性
    }

    @Test
    void testLargeDataInput() {
        // 测试大数据输入
        byte[] largeData = new byte[188 * 10]; // 10个TS包

        // 填充多个TS包
        for (int i = 0; i < 10; i++) {
            byte[] packet = createBasicTSPacket();
            System.arraycopy(packet, 0, largeData, i * 188, 188);
        }

        assertTrue(decoder.input(largeData, 0, largeData.length));
    }
    
    /**
     * 创建基本的TS包
     */
    private byte[] createBasicTSPacket() {
        byte[] packet = new byte[188];
        
        // TS包头部
        packet[0] = 0x47; // 同步字节
        packet[1] = 0x00; // PID高位
        packet[2] = 0x00; // PID低位
        packet[3] = 0x10; // 连续计数器=0, 无适配字段
        
        // 填充负载
        for (int i = 4; i < 188; i++) {
            packet[i] = (byte) 0xFF; // 填充字节
        }
        
        return packet;
    }
    
    /**
     * 创建PAT包
     */
    private byte[] createPATPacket() {
        byte[] packet = new byte[188];
        
        // TS包头部 (PID=0)
        packet[0] = 0x47; // 同步字节
        packet[1] = 0x40; // 载荷单元开始标志=1, PID高位=0
        packet[2] = 0x00; // PID低位=0
        packet[3] = 0x10; // 连续计数器=0
        
        // PAT负载
        packet[4] = 0x00; // 指针字段
        packet[5] = 0x00; // 表ID
        packet[6] = (byte) 0xB0; // 段语法标志=1
        packet[7] = 0x0D; // 段长度
        
        // 填充其余部分
        for (int i = 8; i < 188; i++) {
            packet[i] = (byte) 0xFF;
        }
        
        return packet;
    }
    
    /**
     * 创建PMT包
     */
    private byte[] createPMTPacket() {
        byte[] packet = new byte[188];
        
        // TS包头部 (PID=0x100)
        packet[0] = 0x47; // 同步字节
        packet[1] = 0x41; // 载荷单元开始标志=1, PID高位=1
        packet[2] = 0x00; // PID低位=0
        packet[3] = 0x10; // 连续计数器=0
        
        // PMT负载
        packet[4] = 0x00; // 指针字段
        packet[5] = 0x02; // 表ID (PMT)
        packet[6] = (byte) 0xB0; // 段语法标志=1
        packet[7] = 0x12; // 段长度
        
        // 填充其余部分
        for (int i = 8; i < 188; i++) {
            packet[i] = (byte) 0xFF;
        }
        
        return packet;
    }
    
    /**
     * 创建带指定PID和连续计数器的TS包
     */
    private byte[] createTSPacketWithCC(int pid, int cc) {
        byte[] packet = new byte[188];
        
        packet[0] = 0x47; // 同步字节
        packet[1] = (byte) ((pid >> 8) & 0xFF);
        packet[2] = (byte) (pid & 0xFF);
        packet[3] = (byte) (0x10 | (cc & 0x0F)); // 连续计数器
        
        // 填充负载
        for (int i = 4; i < 188; i++) {
            packet[i] = (byte) 0xFF;
        }
        
        return packet;
    }
}
