package com.zlmediakit.gb28181.network;

import com.zlmediakit.gb28181.GB28181Server.TcpMode;
import com.zlmediakit.gb28181.media.Frame;
import com.zlmediakit.gb28181.media.MediaTuple;
import com.zlmediakit.gb28181.session.RtpSession;
import com.zlmediakit.gb28181.session.SessionManager;
import io.netty.bootstrap.Bootstrap;
import io.netty.bootstrap.ServerBootstrap;
import io.netty.channel.*;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.nio.NioDatagramChannel;
import io.netty.channel.socket.nio.NioServerSocketChannel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.InetSocketAddress;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Consumer;

/**
 * RTP服务器
 * 基于ZLMediaKit的RtpServer实现
 * 支持UDP和TCP两种传输模式
 * 
 * <AUTHOR> Java Port
 */
public class RtpServer {
    private static final Logger logger = LoggerFactory.getLogger(RtpServer.class);
    
    // Netty相关
    private EventLoopGroup bossGroup;
    private EventLoopGroup workerGroup;
    private Channel udpChannel;
    private Channel tcpChannel;
    
    // 配置参数
    private int port = 10000;
    private String localIp = "0.0.0.0";
    private TcpMode tcpMode = TcpMode.PASSIVE;
    private boolean reusePort = true;
    private boolean multiplex = false;
    private int timeoutSec = 15;
    private long ssrc = 0;
    private int onlyTrack = 0; // 0=all, 1=audio, 2=video
    
    // 状态管理
    private final AtomicBoolean running = new AtomicBoolean(false);
    private final ConcurrentHashMap<String, RtpSession> sessions = new ConcurrentHashMap<>();
    
    // 会话管理器
    private SessionManager sessionManager;
    
    // 回调函数
    private Consumer<Frame> onFrameCallback;
    private Consumer<RtpSession> onSessionCreateCallback;
    private Consumer<RtpSession> onSessionDestroyCallback;
    
    /**
     * 构造函数
     */
    public RtpServer() {
        this.sessionManager = new SessionManager();
    }
    
    /**
     * 启动RTP服务器
     * 
     * @throws Exception 启动异常
     */
    public void start() throws Exception {
        if (running.get()) {
            logger.warn("RtpServer is already running");
            return;
        }
        
        logger.info("Starting RtpServer on {}:{}, tcpMode={}, multiplex={}", 
                   localIp, port, tcpMode, multiplex);
        
        // 创建事件循环组
        bossGroup = new NioEventLoopGroup(1);
        workerGroup = new NioEventLoopGroup();
        
        try {
            // 启动UDP服务器
            startUdpServer();
            
            // 根据TCP模式启动TCP服务器
            if (tcpMode != TcpMode.NONE) {
                startTcpServer();
            }
            
            // 启动会话管理器
            sessionManager.start();
            
            running.set(true);
            logger.info("RtpServer started successfully");
            
        } catch (Exception e) {
            logger.error("Failed to start RtpServer", e);
            stop();
            throw e;
        }
    }
    
    /**
     * 停止RTP服务器
     */
    public void stop() {
        if (!running.get()) {
            return;
        }
        
        logger.info("Stopping RtpServer");
        
        running.set(false);
        
        // 关闭所有会话
        sessions.values().forEach(RtpSession::close);
        sessions.clear();
        
        // 停止会话管理器
        if (sessionManager != null) {
            sessionManager.shutdown();
        }
        
        // 关闭网络通道
        if (udpChannel != null) {
            udpChannel.close();
        }
        if (tcpChannel != null) {
            tcpChannel.close();
        }
        
        // 关闭事件循环组
        if (workerGroup != null) {
            workerGroup.shutdownGracefully();
        }
        if (bossGroup != null) {
            bossGroup.shutdownGracefully();
        }
        
        logger.info("RtpServer stopped");
    }
    
    /**
     * 启动UDP服务器
     */
    private void startUdpServer() throws Exception {
        Bootstrap bootstrap = new Bootstrap();
        bootstrap.group(workerGroup)
                .channel(NioDatagramChannel.class)
                .option(ChannelOption.SO_BROADCAST, true)
                .option(ChannelOption.SO_REUSEADDR, reusePort)
                .handler(new UdpRtpReceiver(this));
        
        ChannelFuture future = bootstrap.bind(localIp, port);
        udpChannel = future.sync().channel();
        
        logger.info("UDP RTP server started on {}:{}", localIp, port);
    }
    
    /**
     * 启动TCP服务器
     */
    private void startTcpServer() throws Exception {
        if (tcpMode == TcpMode.PASSIVE) {
            // 被动模式：作为服务器监听连接
            ServerBootstrap bootstrap = new ServerBootstrap();
            bootstrap.group(bossGroup, workerGroup)
                    .channel(NioServerSocketChannel.class)
                    .option(ChannelOption.SO_BACKLOG, 128)
                    .option(ChannelOption.SO_REUSEADDR, reusePort)
                    .childOption(ChannelOption.SO_KEEPALIVE, true)
                    .childOption(ChannelOption.TCP_NODELAY, true)
                    .childHandler(new TcpRtpReceiver(this));
            
            ChannelFuture future = bootstrap.bind(localIp, port + 1); // TCP端口+1
            tcpChannel = future.sync().channel();
            
            logger.info("TCP RTP server started on {}:{}", localIp, port + 1);
        } else {
            // 主动模式：作为客户端连接
            logger.info("TCP active mode not implemented yet");
        }
    }
    
    /**
     * 创建RTP会话
     * 
     * @param remoteAddress 远程地址
     * @param isUdp 是否UDP模式
     * @return RtpSession
     */
    public RtpSession createSession(InetSocketAddress remoteAddress, boolean isUdp) {
        String sessionKey = generateSessionKey(remoteAddress, isUdp);
        
        RtpSession session = sessions.get(sessionKey);
        if (session != null) {
            return session;
        }
        
        // 创建媒体元组
        MediaTuple mediaTuple = new MediaTuple();
        mediaTuple.setVhost("__defaultVhost__");
        mediaTuple.setApp("rtp");
        mediaTuple.setStream(sessionKey);
        
        // 创建新会话
        session = new RtpSession(mediaTuple, remoteAddress, isUdp);
        session.setTimeoutSec(timeoutSec);
        session.setSsrc(ssrc);
        session.setOnlyTrack(onlyTrack);

        // 设置回调
        if (onFrameCallback != null) {
            session.setOnFrame(onFrameCallback);
        }

        final RtpSession finalSession = session;
        session.setOnDetach((ex) -> {
            sessions.remove(sessionKey);
            if (onSessionDestroyCallback != null) {
                onSessionDestroyCallback.accept(finalSession);
            }
            logger.debug("Session {} detached: {}", sessionKey, ex.getMessage());
        });
        
        sessions.put(sessionKey, session);
        
        // 添加到会话管理器
        sessionManager.addSession(session);
        
        if (onSessionCreateCallback != null) {
            onSessionCreateCallback.accept(session);
        }
        
        logger.debug("Created session: {}", sessionKey);
        return session;
    }
    
    /**
     * 获取RTP会话
     * 
     * @param remoteAddress 远程地址
     * @param isUdp 是否UDP模式
     * @return RtpSession
     */
    public RtpSession getSession(InetSocketAddress remoteAddress, boolean isUdp) {
        String sessionKey = generateSessionKey(remoteAddress, isUdp);
        return sessions.get(sessionKey);
    }
    
    /**
     * 生成会话键
     * 
     * @param remoteAddress 远程地址
     * @param isUdp 是否UDP模式
     * @return 会话键
     */
    private String generateSessionKey(InetSocketAddress remoteAddress, boolean isUdp) {
        return String.format("%s:%d_%s", 
                           remoteAddress.getHostString(), 
                           remoteAddress.getPort(), 
                           isUdp ? "udp" : "tcp");
    }
    
    /**
     * 检查服务器是否运行中
     * 
     * @return boolean
     */
    public boolean isRunning() {
        return running.get();
    }
    
    // Getter和Setter方法
    
    public int getPort() {
        return port;
    }
    
    public void setPort(int port) {
        this.port = port;
    }
    
    public String getLocalIp() {
        return localIp;
    }
    
    public void setLocalIp(String localIp) {
        this.localIp = localIp;
    }
    
    public TcpMode getTcpMode() {
        return tcpMode;
    }
    
    public void setTcpMode(TcpMode tcpMode) {
        this.tcpMode = tcpMode;
    }
    
    public boolean isReusePort() {
        return reusePort;
    }
    
    public void setReusePort(boolean reusePort) {
        this.reusePort = reusePort;
    }
    
    public boolean isMultiplex() {
        return multiplex;
    }
    
    public void setMultiplex(boolean multiplex) {
        this.multiplex = multiplex;
    }
    
    public int getTimeoutSec() {
        return timeoutSec;
    }
    
    public void setTimeoutSec(int timeoutSec) {
        this.timeoutSec = timeoutSec;
    }
    
    public long getSsrc() {
        return ssrc;
    }
    
    public void setSsrc(long ssrc) {
        this.ssrc = ssrc;
    }
    
    public int getOnlyTrack() {
        return onlyTrack;
    }
    
    public void setOnlyTrack(int onlyTrack) {
        this.onlyTrack = onlyTrack;
    }
    
    public void setOnFrame(Consumer<Frame> callback) {
        this.onFrameCallback = callback;
    }
    
    public void setOnSessionCreate(Consumer<RtpSession> callback) {
        this.onSessionCreateCallback = callback;
    }
    
    public void setOnSessionDestroy(Consumer<RtpSession> callback) {
        this.onSessionDestroyCallback = callback;
    }
}
