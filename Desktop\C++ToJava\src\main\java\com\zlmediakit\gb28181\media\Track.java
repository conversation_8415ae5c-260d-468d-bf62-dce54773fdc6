package com.zlmediakit.gb28181.media;

/**
 * 媒体轨道接口
 * 基于ZLMediaKit的Track设计
 * 
 * <AUTHOR> Java Port
 */
public interface Track {
    
    /**
     * 获取轨道类型
     * 
     * @return 轨道类型
     */
    Frame.TrackType getTrackType();
    
    /**
     * 获取编解码器ID
     * 
     * @return 编解码器ID
     */
    Frame.CodecId getCodecId();
    
    /**
     * 获取编解码器名称
     * 
     * @return 编解码器名称
     */
    String getCodecName();
    
    /**
     * 获取轨道ID
     * 
     * @return 轨道ID
     */
    int getTrackId();
    
    /**
     * 设置轨道ID
     * 
     * @param trackId 轨道ID
     */
    void setTrackId(int trackId);
    
    /**
     * 获取比特率
     * 
     * @return 比特率(bps)
     */
    int getBitRate();
    
    /**
     * 设置比特率
     * 
     * @param bitRate 比特率(bps)
     */
    void setBitRate(int bitRate);
    
    /**
     * 获取帧率
     * 
     * @return 帧率(fps)
     */
    double getFrameRate();
    
    /**
     * 设置帧率
     * 
     * @param frameRate 帧率(fps)
     */
    void setFrameRate(double frameRate);
    
    /**
     * 获取配置帧
     * 
     * @return 配置帧，如果没有返回null
     */
    Frame getConfigFrame();
    
    /**
     * 设置配置帧
     * 
     * @param configFrame 配置帧
     */
    void setConfigFrame(Frame configFrame);
    
    /**
     * 检查是否就绪
     * 
     * @return boolean
     */
    boolean isReady();
    
    /**
     * 输入帧
     * 
     * @param frame 帧
     * @return 是否处理成功
     */
    boolean inputFrame(Frame frame);
    
    /**
     * 获取统计信息
     * 
     * @return 统计信息
     */
    String getStatistics();
    
    /**
     * 重置统计信息
     */
    void resetStatistics();
}
