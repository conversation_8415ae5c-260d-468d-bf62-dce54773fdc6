package com.zlmediakit.gb28181.rtp;

/**
 * RTP包封装类
 * 基于ZLMediaKit的RtpPacket实现
 * 
 * <AUTHOR> Java Port
 */
public class RtpPacket {
    private final RtpHeader header;
    private final byte[] payload;
    private final byte[] rawData;
    private final long receiveTime;
    
    /**
     * 构造函数
     * 
     * @param data RTP包原始数据
     * @param offset 偏移量
     * @param length 长度
     */
    public RtpPacket(byte[] data, int offset, int length) {
        this.receiveTime = System.currentTimeMillis();
        
        // 复制原始数据
        this.rawData = new byte[length];
        System.arraycopy(data, offset, this.rawData, 0, length);
        
        // 解析头部
        this.header = new RtpHeader(this.rawData, 0);
        
        // 提取负载
        this.payload = header.getPayload();
    }
    
    /**
     * 构造函数
     * 
     * @param data RTP包原始数据
     */
    public RtpPacket(byte[] data) {
        this(data, 0, data.length);
    }
    
    /**
     * 获取RTP头部
     * 
     * @return RTP头部
     */
    public RtpHeader getHeader() {
        return header;
    }
    
    /**
     * 获取负载数据
     * 
     * @return 负载数据
     */
    public byte[] getPayload() {
        return payload;
    }
    
    /**
     * 获取负载长度
     * 
     * @return 负载长度
     */
    public int getPayloadLength() {
        return payload != null ? payload.length : 0;
    }
    
    /**
     * 获取原始数据
     * 
     * @return 原始数据
     */
    public byte[] getRawData() {
        return rawData;
    }
    
    /**
     * 获取包总长度
     * 
     * @return 包总长度
     */
    public int getTotalLength() {
        return rawData != null ? rawData.length : 0;
    }
    
    /**
     * 获取接收时间
     * 
     * @return 接收时间戳
     */
    public long getReceiveTime() {
        return receiveTime;
    }
    
    /**
     * 获取序列号
     * 
     * @return 序列号
     */
    public int getSequenceNumber() {
        return header.getSequenceNumber();
    }
    
    /**
     * 获取时间戳
     * 
     * @return 时间戳
     */
    public long getTimestamp() {
        return header.getTimestamp();
    }
    
    /**
     * 获取SSRC
     * 
     * @return SSRC
     */
    public long getSsrc() {
        return header.getSsrc();
    }
    
    /**
     * 获取负载类型
     * 
     * @return 负载类型
     */
    public int getPayloadType() {
        return header.getPayloadType();
    }
    
    /**
     * 是否为标记包
     * 
     * @return boolean
     */
    public boolean isMarker() {
        return header.isMarker();
    }
    
    /**
     * 检查序列号是否连续
     * 
     * @param lastSeq 上一个序列号
     * @return boolean
     */
    public boolean isSequential(int lastSeq) {
        int expectedSeq = (lastSeq + 1) & 0xFFFF;
        return getSequenceNumber() == expectedSeq;
    }
    
    /**
     * 计算序列号差值
     * 
     * @param otherSeq 另一个序列号
     * @return 差值
     */
    public int getSequenceDiff(int otherSeq) {
        int thisSeq = getSequenceNumber();
        int diff = thisSeq - otherSeq;
        
        // 处理序列号回绕
        if (diff > 32768) {
            diff -= 65536;
        } else if (diff < -32768) {
            diff += 65536;
        }
        
        return diff;
    }
    
    /**
     * 检查是否为重复包
     * 
     * @param otherPacket 另一个包
     * @return boolean
     */
    public boolean isDuplicate(RtpPacket otherPacket) {
        return otherPacket != null &&
               getSsrc() == otherPacket.getSsrc() &&
               getSequenceNumber() == otherPacket.getSequenceNumber() &&
               getTimestamp() == otherPacket.getTimestamp();
    }
    
    /**
     * 创建RTP包
     * 
     * @param data 原始数据
     * @param offset 偏移量
     * @param length 长度
     * @return RTP包，如果数据无效返回null
     */
    public static RtpPacket create(byte[] data, int offset, int length) {
        try {
            if (!RtpHeader.isValidRtpPacket(data, offset, length)) {
                return null;
            }
            return new RtpPacket(data, offset, length);
        } catch (Exception e) {
            return null;
        }
    }
    
    /**
     * 创建RTP包
     * 
     * @param data 原始数据
     * @return RTP包，如果数据无效返回null
     */
    public static RtpPacket create(byte[] data) {
        return create(data, 0, data.length);
    }
    
    @Override
    public String toString() {
        return "RtpPacket{" +
               "seq=" + getSequenceNumber() +
               ", timestamp=" + getTimestamp() +
               ", ssrc=" + getSsrc() +
               ", pt=" + getPayloadType() +
               ", marker=" + isMarker() +
               ", payloadLength=" + getPayloadLength() +
               ", totalLength=" + getTotalLength() +
               ", receiveTime=" + receiveTime +
               '}';
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        
        RtpPacket rtpPacket = (RtpPacket) obj;
        return getSsrc() == rtpPacket.getSsrc() &&
               getSequenceNumber() == rtpPacket.getSequenceNumber() &&
               getTimestamp() == rtpPacket.getTimestamp();
    }
    
    @Override
    public int hashCode() {
        int result = Long.hashCode(getSsrc());
        result = 31 * result + getSequenceNumber();
        result = 31 * result + Long.hashCode(getTimestamp());
        return result;
    }
}
