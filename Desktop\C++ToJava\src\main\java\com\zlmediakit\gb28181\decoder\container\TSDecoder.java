package com.zlmediakit.gb28181.decoder.container;

import com.zlmediakit.gb28181.media.Frame;
import com.zlmediakit.gb28181.media.FrameImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.nio.ByteBuffer;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Consumer;

/**
 * TS流解码器
 * 基于ZLMediaKit的TSDecoder实现
 * 支持MPEG-TS容器格式解析
 * 
 * <AUTHOR> Java Port
 */
public class TSDecoder implements ContainerDecoder {
    private static final Logger logger = LoggerFactory.getLogger(TSDecoder.class);
    
    // TS包常量
    private static final int TS_PACKET_SIZE = 188;
    private static final int TS_SYNC_BYTE = 0x47;
    
    // PID常量
    private static final int PAT_PID = 0x0000;
    private static final int PMT_PID_START = 0x0020;
    private static final int PMT_PID_END = 0x1FFE;
    
    // 流类型常量
    private static final int STREAM_TYPE_H264 = 0x1B;
    private static final int STREAM_TYPE_H265 = 0x24;
    private static final int STREAM_TYPE_AAC = 0x0F;
    private static final int STREAM_TYPE_MP3 = 0x03;
    
    // 缓冲区
    private final ByteBuffer inputBuffer;
    private final ConcurrentHashMap<Integer, TSStream> streams = new ConcurrentHashMap<>();
    
    // 回调函数
    private Consumer<Frame> onFrameCallback;
    
    // 状态变量
    private boolean ready = true;
    private int pmtPid = -1;
    
    // 统计信息
    private final AtomicLong totalPackets = new AtomicLong(0);
    private final AtomicLong totalBytes = new AtomicLong(0);
    private final AtomicLong outputFrames = new AtomicLong(0);
    private final AtomicLong errorPackets = new AtomicLong(0);
    
    /**
     * TS流信息
     */
    private static class TSStream {
        int pid;
        int streamType;
        Frame.CodecId codecId;
        ByteBuffer pesBuffer;
        boolean pesStarted;
        int continuityCounter;
        long lastPts = -1;
        long lastDts = -1;
        
        TSStream(int pid, int streamType) {
            this.pid = pid;
            this.streamType = streamType;
            this.codecId = getCodecIdFromStreamType(streamType);
            this.pesBuffer = ByteBuffer.allocate(1024 * 1024); // 1MB PES缓冲区
            this.continuityCounter = -1;
        }
        
        private static Frame.CodecId getCodecIdFromStreamType(int streamType) {
            switch (streamType) {
                case STREAM_TYPE_H264:
                    return Frame.CodecId.CodecH264;
                case STREAM_TYPE_H265:
                    return Frame.CodecId.CodecH265;
                case STREAM_TYPE_AAC:
                    return Frame.CodecId.CodecAAC;
                case STREAM_TYPE_MP3:
                    return Frame.CodecId.CodecInvalid; // 暂不支持MP3
                default:
                    return Frame.CodecId.CodecInvalid;
            }
        }
    }
    
    /**
     * 构造函数
     */
    public TSDecoder() {
        this.inputBuffer = ByteBuffer.allocate(64 * 1024); // 64KB输入缓冲区
        
        logger.debug("Created TSDecoder");
    }
    
    @Override
    public boolean input(byte[] data) {
        return input(data, 0, data.length);
    }
    
    @Override
    public boolean input(byte[] data, int offset, int length) {
        if (data == null || length <= 0 || !ready) {
            return false;
        }
        
        try {
            totalBytes.addAndGet(length);
            
            // 添加数据到输入缓冲区
            if (inputBuffer.remaining() < length) {
                // 压缩缓冲区
                inputBuffer.compact();
                
                // 如果还是不够空间，扩展缓冲区或清空
                if (inputBuffer.remaining() < length) {
                    logger.warn("Input buffer overflow, clearing buffer");
                    inputBuffer.clear();
                }
            }
            
            inputBuffer.put(data, offset, length);
            
            // 处理TS包
            processTS();
            
            return true;
            
        } catch (Exception e) {
            logger.error("Error processing TS data", e);
            errorPackets.incrementAndGet();
            return false;
        }
    }
    
    /**
     * 处理TS包
     */
    private void processTS() {
        inputBuffer.flip(); // 切换到读模式
        
        while (inputBuffer.remaining() >= TS_PACKET_SIZE) {
            // 查找同步字节
            if (inputBuffer.get(inputBuffer.position()) != TS_SYNC_BYTE) {
                // 同步字节不匹配，跳过一个字节
                inputBuffer.get();
                continue;
            }
            
            // 检查是否有完整的TS包
            if (inputBuffer.remaining() < TS_PACKET_SIZE) {
                break;
            }
            
            // 读取TS包
            byte[] tsPacket = new byte[TS_PACKET_SIZE];
            inputBuffer.get(tsPacket);
            
            // 处理TS包
            processTSPacket(tsPacket);
            totalPackets.incrementAndGet();
        }
        
        inputBuffer.compact(); // 切换到写模式，保留未处理的数据
    }
    
    /**
     * 处理单个TS包
     * 
     * @param packet TS包数据
     */
    private void processTSPacket(byte[] packet) {
        if (packet.length != TS_PACKET_SIZE || packet[0] != TS_SYNC_BYTE) {
            return;
        }
        
        // 解析TS头部
        int transportErrorIndicator = (packet[1] & 0x80) >> 7;
        int payloadUnitStartIndicator = (packet[1] & 0x40) >> 6;
        int transportPriority = (packet[1] & 0x20) >> 5;
        int pid = ((packet[1] & 0x1F) << 8) | (packet[2] & 0xFF);
        int transportScramblingControl = (packet[3] & 0xC0) >> 6;
        int adaptationFieldControl = (packet[3] & 0x30) >> 4;
        int continuityCounter = packet[3] & 0x0F;
        
        // 检查传输错误
        if (transportErrorIndicator != 0) {
            errorPackets.incrementAndGet();
            return;
        }
        
        // 计算负载偏移
        int payloadOffset = 4;
        
        // 处理自适应字段
        if (adaptationFieldControl == 2 || adaptationFieldControl == 3) {
            int adaptationFieldLength = packet[4] & 0xFF;
            payloadOffset += 1 + adaptationFieldLength;
        }
        
        // 检查是否有负载
        if (adaptationFieldControl == 1 || adaptationFieldControl == 3) {
            if (payloadOffset < TS_PACKET_SIZE) {
                int payloadLength = TS_PACKET_SIZE - payloadOffset;
                processPayload(pid, packet, payloadOffset, payloadLength, 
                              payloadUnitStartIndicator != 0, continuityCounter);
            }
        }
    }
    
    /**
     * 处理负载数据
     * 
     * @param pid PID
     * @param packet TS包数据
     * @param offset 负载偏移
     * @param length 负载长度
     * @param payloadStart 是否负载开始
     * @param continuityCounter 连续计数器
     */
    private void processPayload(int pid, byte[] packet, int offset, int length, 
                               boolean payloadStart, int continuityCounter) {
        
        if (pid == PAT_PID) {
            // 处理PAT表
            processPAT(packet, offset, length, payloadStart);
        } else if (pid == pmtPid) {
            // 处理PMT表
            processPMT(packet, offset, length, payloadStart);
        } else {
            // 处理ES流
            TSStream stream = streams.get(pid);
            if (stream != null) {
                processES(stream, packet, offset, length, payloadStart, continuityCounter);
            }
        }
    }
    
    /**
     * 处理PAT表
     */
    private void processPAT(byte[] packet, int offset, int length, boolean payloadStart) {
        if (!payloadStart || length < 8) {
            return;
        }
        
        try {
            // 跳过指针字段
            int pointerField = packet[offset] & 0xFF;
            offset += 1 + pointerField;
            length -= 1 + pointerField;
            
            if (length < 8) {
                return;
            }
            
            // 解析PAT头部
            int tableId = packet[offset] & 0xFF;
            if (tableId != 0x00) {
                return; // 不是PAT表
            }
            
            int sectionLength = ((packet[offset + 1] & 0x0F) << 8) | (packet[offset + 2] & 0xFF);
            
            // 查找PMT PID
            int dataOffset = offset + 8;
            int dataEnd = offset + 3 + sectionLength - 4; // 减去CRC32
            
            while (dataOffset + 4 <= dataEnd) {
                int programNumber = ((packet[dataOffset] & 0xFF) << 8) | (packet[dataOffset + 1] & 0xFF);
                int programPid = ((packet[dataOffset + 2] & 0x1F) << 8) | (packet[dataOffset + 3] & 0xFF);
                
                if (programNumber != 0) { // 不是网络PID
                    pmtPid = programPid;
                    logger.debug("Found PMT PID: {}", pmtPid);
                    break;
                }
                
                dataOffset += 4;
            }
            
        } catch (Exception e) {
            logger.error("Error processing PAT", e);
        }
    }
    
    /**
     * 处理PMT表
     */
    private void processPMT(byte[] packet, int offset, int length, boolean payloadStart) {
        if (!payloadStart || length < 12) {
            return;
        }
        
        try {
            // 跳过指针字段
            int pointerField = packet[offset] & 0xFF;
            offset += 1 + pointerField;
            length -= 1 + pointerField;
            
            if (length < 12) {
                return;
            }
            
            // 解析PMT头部
            int tableId = packet[offset] & 0xFF;
            if (tableId != 0x02) {
                return; // 不是PMT表
            }
            
            int sectionLength = ((packet[offset + 1] & 0x0F) << 8) | (packet[offset + 2] & 0xFF);
            int programInfoLength = ((packet[offset + 10] & 0x0F) << 8) | (packet[offset + 11] & 0xFF);
            
            // 解析ES流信息
            int dataOffset = offset + 12 + programInfoLength;
            int dataEnd = offset + 3 + sectionLength - 4; // 减去CRC32
            
            while (dataOffset + 5 <= dataEnd) {
                int streamType = packet[dataOffset] & 0xFF;
                int elementaryPid = ((packet[dataOffset + 1] & 0x1F) << 8) | (packet[dataOffset + 2] & 0xFF);
                int esInfoLength = ((packet[dataOffset + 3] & 0x0F) << 8) | (packet[dataOffset + 4] & 0xFF);
                
                // 创建流
                TSStream stream = new TSStream(elementaryPid, streamType);
                if (stream.codecId != Frame.CodecId.CodecInvalid) {
                    streams.put(elementaryPid, stream);
                    logger.debug("Found ES stream: PID={}, type={}, codec={}", 
                               elementaryPid, streamType, stream.codecId);
                }
                
                dataOffset += 5 + esInfoLength;
            }
            
        } catch (Exception e) {
            logger.error("Error processing PMT", e);
        }
    }
    
    /**
     * 处理ES流
     */
    private void processES(TSStream stream, byte[] packet, int offset, int length, 
                          boolean payloadStart, int continuityCounter) {
        
        // 检查连续计数器
        if (stream.continuityCounter != -1) {
            int expectedCounter = (stream.continuityCounter + 1) & 0x0F;
            if (continuityCounter != expectedCounter) {
                logger.debug("Continuity counter error for PID {}: expected={}, actual={}", 
                           stream.pid, expectedCounter, continuityCounter);
                // 重置PES缓冲区
                stream.pesBuffer.clear();
                stream.pesStarted = false;
            }
        }
        stream.continuityCounter = continuityCounter;
        
        if (payloadStart) {
            // PES包开始
            if (stream.pesBuffer.position() > 0) {
                // 输出之前的PES包
                outputPES(stream);
            }
            
            stream.pesStarted = true;
            stream.pesBuffer.clear();
        }
        
        if (stream.pesStarted && stream.pesBuffer.remaining() >= length) {
            stream.pesBuffer.put(packet, offset, length);
        }
    }
    
    /**
     * 输出PES包
     */
    private void outputPES(TSStream stream) {
        if (stream.pesBuffer.position() < 6) {
            return; // PES包太短
        }
        
        byte[] pesData = new byte[stream.pesBuffer.position()];
        stream.pesBuffer.flip();
        stream.pesBuffer.get(pesData);
        stream.pesBuffer.clear();
        
        // 解析PES头部
        if (pesData[0] == 0x00 && pesData[1] == 0x00 && pesData[2] == 0x01) {
            int streamId = pesData[3] & 0xFF;
            int pesPacketLength = ((pesData[4] & 0xFF) << 8) | (pesData[5] & 0xFF);
            
            // 提取ES数据
            int esOffset = 6;
            if (pesData.length > 8 && (pesData[6] & 0xC0) == 0x80) {
                // 有PES扩展头
                int pesHeaderLength = pesData[8] & 0xFF;
                esOffset = 9 + pesHeaderLength;
                
                // 解析PTS/DTS
                parsePTSDTS(stream, pesData, 9, pesHeaderLength);
            }
            
            if (esOffset < pesData.length) {
                byte[] esData = new byte[pesData.length - esOffset];
                System.arraycopy(pesData, esOffset, esData, 0, esData.length);
                
                // 输出帧
                outputFrame(stream, esData);
            }
        }
    }
    
    /**
     * 解析PTS/DTS
     */
    private void parsePTSDTS(TSStream stream, byte[] pesData, int offset, int length) {
        if (length < 5) {
            return;
        }
        
        int ptsFlags = (pesData[offset - 2] & 0xC0) >> 6;
        
        if (ptsFlags >= 2) {
            // 有PTS
            long pts = ((long)(pesData[offset] & 0x0E) << 29) |
                      ((long)(pesData[offset + 1] & 0xFF) << 22) |
                      ((long)(pesData[offset + 2] & 0xFE) << 14) |
                      ((long)(pesData[offset + 3] & 0xFF) << 7) |
                      ((long)(pesData[offset + 4] & 0xFE) >> 1);
            
            stream.lastPts = pts;
            
            if (ptsFlags == 3 && length >= 10) {
                // 有DTS
                long dts = ((long)(pesData[offset + 5] & 0x0E) << 29) |
                          ((long)(pesData[offset + 6] & 0xFF) << 22) |
                          ((long)(pesData[offset + 7] & 0xFE) << 14) |
                          ((long)(pesData[offset + 8] & 0xFF) << 7) |
                          ((long)(pesData[offset + 9] & 0xFE) >> 1);
                
                stream.lastDts = dts;
            } else {
                stream.lastDts = pts;
            }
        }
    }
    
    /**
     * 输出帧
     */
    private void outputFrame(TSStream stream, byte[] data) {
        if (onFrameCallback == null || data == null || data.length == 0) {
            return;
        }
        
        try {
            // 转换时间戳（90kHz -> 毫秒）
            long dts = stream.lastDts != -1 ? stream.lastDts / 90 : 0;
            long pts = stream.lastPts != -1 ? stream.lastPts / 90 : dts;
            
            // 创建帧
            FrameImpl frame = new FrameImpl(stream.codecId, data, dts, pts);
            frame.setIndex(stream.pid);
            
            // 调用回调
            onFrameCallback.accept(frame);
            outputFrames.incrementAndGet();
            
            logger.debug("Output TS frame: PID={}, codec={}, size={}, dts={}, pts={}", 
                        stream.pid, stream.codecId, data.length, dts, pts);
            
        } catch (Exception e) {
            logger.error("Error outputting TS frame", e);
        }
    }
    
    @Override
    public void setOnFrame(Consumer<Frame> callback) {
        this.onFrameCallback = callback;
    }
    
    @Override
    public void flush() {
        // 输出所有未完成的PES包
        streams.values().forEach(this::outputPES);
        reset();
    }
    
    @Override
    public String getDecoderName() {
        return "TSDecoder";
    }
    
    @Override
    public String getStatistics() {
        StringBuilder sb = new StringBuilder();
        sb.append("TSDecoder Statistics:\n");
        sb.append("  Total Packets: ").append(totalPackets.get()).append("\n");
        sb.append("  Total Bytes: ").append(totalBytes.get()).append("\n");
        sb.append("  Output Frames: ").append(outputFrames.get()).append("\n");
        sb.append("  Error Packets: ").append(errorPackets.get()).append("\n");
        sb.append("  Streams: ").append(streams.size()).append("\n");
        sb.append("  PMT PID: ").append(pmtPid).append("\n");
        sb.append("  Ready: ").append(ready).append("\n");
        
        return sb.toString();
    }
    
    @Override
    public void reset() {
        inputBuffer.clear();
        streams.clear();
        pmtPid = -1;
        ready = true;
        
        logger.debug("TSDecoder reset");
    }
    
    @Override
    public boolean isReady() {
        return ready;
    }
}
